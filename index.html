<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- 配置MathJax -->
    <!-- <script
      type="text/javascript"
      async
      src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML"
    ></script> -->
    <link rel="stylesheet" href="./katex.min.css" />

    <!-- The loading of KaTeX is deferred to speed up page rendering -->
    <script defer src="./katex.min.js" type="module"></script>

    <!-- To automatically render math in text elements, include the auto-render extension: -->
    <script
      defer
      src="./contrib/auto-render.min.js"
      type="module"
      onload="renderMathInElement(document.body);"
    ></script>
    <script defer type="module" src="./contrib/mhchem.min.js"></script>
    <script type="module" src="./contrib/copy-tex.min.js"></script>
    <title>问题中心</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <!-- <script type="text/x-mathjax-config">
      MathJax.Hub.Config({
        showProcessingMessages: false,
        //extensions: ['jsMath2jax.js', 'tex2jax.js'],
        extensions: ['tex2jax.js'],
        showMathMenu: false,
        tex2jax: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)']
          ],
          displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]']
          ],
          processEscapes: true
        },
      });
    </script> -->
  </body>
</html>
