<template>
    <el-dialog v-model="questionDialogVisible" width="910" top="90px">
        <template #header>
            <div class="header">
                <span>查看问题</span>
                <span class="back" @click="goBackAnswerDrawer">返回</span>
            </div>
        </template>
        <div class="main-wrapper">
            <span class="tip">
                <span class="ques-username">
                    <b>{{ question.userName }}</b>
                </span>
                的提问</span>
            <div class="related-content-container" style="background-color: white; display: flex;">
                <b class="ques-mark">【</b><span style="max-width: 90%; overflow: hidden"
                    class="questionList markdown-content" v-html="(question.keyword)"></span><b
                    class="ques-mark">】</b>
                <span class="q-type">{{ question.questionType }}?</span>
            </div>
            <div v-if="qTypeDict[question.questionType] === qType.open">
                <span class="ques-decription">
                    <span class="ck-content" v-html="question.questionDescription"></span>
                </span>
            </div>
            <div style="display: flex; width: 100%; margin-bottom: 15px;">
                <div class="ques-timebar">
                    <span>
                        {{ question.createTime }}
                    </span>
                </div>
                <div class="to-answer-btn">
                    <span class="l-btn" @click="goAwerDialog" style="color: var(--color-primary);"> 回答 </span>
                    <span class="l-btn" v-if="question.canDelete" @click="handleDeleteQues" style="margin-left: 25px;"> 删除
                    </span>
                </div>
            </div>
            <div class="middle">
                <div v-for="(item, index) in filterAnswerList" style="width: 100%">
                    <div :class="index === 0 ? answerItemList[0] : answerItemList[1]">
                        <div class="top" v-if="item.pinToTop === 1">置顶</div>
                        <div class="info">
                            <span>
                                <span class="answer-name"><b>{{ item.userName }}</b></span>
                                <span class="author" v-if="item.isAuthor === 1">
                                    作者
                                </span>
                                <span style="margin-left: 5px">的回答</span>
                            </span>
                            <span v-if="item.releaseStatus === 0" class="release-status">
                                未发布
                            </span>
                        </div>
                        <div class="klg-list">
                            <my-tag class="k" :tag-id="klg.code" v-for="klg in item.knowledgeList" :key="klg.code" :type="'klgTag'"
                                :deletable="false">
                                <el-tooltip raw-content popper-class="tooltip-width">
                                    <template #content>
                                        <div class="tooltipHtml ck-content" v-html="klg.title"></div>
                                    </template>
                                    <span class="htmlContent2 ck-content" v-html="klg.title"></span>
                                </el-tooltip>
                            </my-tag>
                        </div>
                        <div class="answer-exp">
                            <span class="markdown-content" v-html="(item.answerExplanation)"></span>
                        </div>
                        <div class="tooltip">
                            <span> {{ item.createTime }}</span>
                            <div>
                                <span v-if="index > 0" style="cursor: pointer; color: var(--color-primary)" class="top-text"
                                    @click="handleTopAnswer(item.answerId)">
                                    置顶
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from '@/stores/dialog';
import { emitter } from '@/utils/emitter';
import { ref, watch } from 'vue';
import { Event } from '@/types/event';
import { qType, qTypeDict } from "@/utils/constant";
import { processAllLatexEquations } from '@/utils/latexUtils';
import { deleteQuestionApi, getAnswersApi, params2DelQues } from '@/apis/path/prjdetail';
import { ElMessage, ElMessageBox } from 'element-plus';
import { TopAnswerApi, params2TopAns } from '@/apis/path/answerList';
import { fa } from 'element-plus/es/locale/index.mjs';

const dialogStore = useDialogStore()
const questionDialogVisible = ref(dialogStore.questionDialogVisible)
const question = ref(dialogStore.question)

const answerList = ref([]);
const filterAnswerList = ref([])
const answerItemList = ["answer-item", "answer-item2"];

watch(
    () => dialogStore.questionDialogVisible,
    (newVal: boolean) => {
        if (newVal == true) {
            getAnswers()
        }
        questionDialogVisible.value = newVal;
    },
    { immediate: true } // 立即执行一次以初始化值
);

watch(
    questionDialogVisible,
    (newVal) => {
        if (newVal !== dialogStore.questionDialogVisible) {
            dialogStore.questionDialogVisible = newVal;
        }
    }
);

watch(
    () => dialogStore.question,
    (newVal) => {
        question.value = newVal
    }
)

// 处理删除问题
const handleDeleteQues = () => {
    if (question.value.canDelete) {
        ElMessageBox.confirm('确定删除问题吗？', '删除问题', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        })
            .then(() => {
                const params = ref<params2DelQues>({
                    questionId: question.value.questionId
                });
                deleteQuestionApi(params.value).then((res) => {
                    if (res.success) {
                        emitter.emit(Event.REMOVE_QUESTION, question.value.questionId);
                        emitter.emit(Event.REFRESH_QUESTION_LIST, true);
                        ElMessage.success('删除成功');
                        dialogStore.questionDialogVisible = false
                    } else {
                        ElMessage.error(res.message);
                    }
                });
            })
            .catch();
    }
};

const goBackAnswerDrawer = () => {
    questionDialogVisible.value = false
    emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: question.value.questionId })
}

const goAwerDialog = () => {
    questionDialogVisible.value = false
    dialogStore.answerDialogVisible = true
}

// 获取答案列表
const getAnswers = () => {
    getAnswersApi(question.value.questionId.toString()).then((res) => {
        if (res.success) {
            Object.assign(question.value, res.data.questionAndAnswer[0]);
            answerList.value = res.data.questionAndAnswer[0].answers;
            filterAnswerList.value = answerList.value.filter(item => item.releaseStatus === 1);
        }
    });
};

const handleTopAnswer = (ansId: number) => {
    ElMessageBox.confirm("确定置顶这条回答吗？", "置顶回答", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
    }).then(() => {
        const params = ref<params2TopAns>({
            answerId: ansId,
        });
        TopAnswerApi(params.value).then((res) => {
            if (res.success) {
                getAnswers();
                ElMessage.success("置顶成功！");
                emitter.emit(Event.REFRESH_QUESTION_LIST, true);
            }
        });
    });
};
</script>

<style scoped lang="less">
.header {
    position: relative;
    width: 880px;
    border-bottom: 1px solid #dcdfe6;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    padding-bottom: 10px;

    .back {
        position: absolute;
        right: 30px;
        font-size: 14px;
        font-weight: 400;
        color: var(--color-primary);
        cursor: pointer;
    }
}

.main-wrapper {
    width: 90%;
    margin: 0 auto;

    .tip {
        margin-bottom: 6px;
        font-size: 12px;

        .ques-username {
            font-weight: 700;
            margin-right: 10px;
        }
    }

    .related-content-container {
        display: flex;
        align-items: center;
        margin-top: 15px;
        margin-bottom: 10px;
        background-color: var(--color-light);
        word-break: break-all;

        .ques-decription {
            display: flex;
            text-align: center;
            min-width: 100%;
            border: 1px solid var(--color-primary);
            border-radius: 5px;
            padding: 10px;
        }

        .ques-username {
            margin-right: 10px;
        }

        .ques-mark {
            display: flex;
            align-items: center;
        }

        .q-type {
            display: inline-block;
            white-space: nowrap;
        }
    }

    .ques-timebar {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: var(--color-grey);
        font-size: 12px;

        .answer {
            font-style: normal;
            font-size: 12px;
            color: #1973cb;
            text-align: right;

            &:hover {
                cursor: pointer;
            }
        }
    }

    .to-answer-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 12px;

        .l-btn:hover {
            cursor: pointer;
            font-weight: bold;
        }
    }

    .middle {
        padding-top: 10px;
        border-top: 1px solid #dcdfe6;

        .top {
            width: 40px;
            height: 20px;
            font-size: 12px;
            text-align: center;
            color: white;
            background-color: var(--color-primary);
            padding: 2px;
            margin-bottom: 5px;
        }

        .answer-item {
            width: 100%;
            padding: 10px 10px;
            //background-color: #f2f2f2;
            border-bottom: 1px solid #dcdfe6;
            margin-bottom: 10px;
            border-radius: 3px;

            .info {
                margin-bottom: 10px;
                font-size: 12px;
                display: flex;
                justify-content: space-between;
                width: 80%;

                .answer-name {
                    font-size: 12px;
                    font-weight: 600;
                    color: #333333;
                }

                .author {
                    margin-left: 5px;
                    background-color: rgba(238, 162, 60, 0.1);
                    border: 1px solid rgba(238, 162, 60, 0.3);
                    color: #e6a23c;
                    border-radius: 4px;
                    height: 8px;
                    padding: 3px 5px;
                }

                .release-status {
                    padding: 0 5px;
                    border: 1px solid;
                    border-color: var(--color-release-status);
                    border-radius: 3px;
                    color: var(--color-release-status);
                    background-color: var(--color-release-status-rgba);
                }
            }
        }

        .answer-item2 {
            width: 100%;
            padding: 10px 10px;
            margin-bottom: 10px;
            border-radius: 3px;
            border-bottom: 1px solid #dcdfe6;

            .answer-name {
                font-size: 12px;
                font-weight: 600;
                color: #333333;
            }

            .info {
                margin-bottom: 10px;
                font-size: 12px;
                display: flex;
                justify-content: space-between;
                width: 80%;

                .author {
                    margin-left: 5px;
                    background-color: rgba(238, 162, 60, 0.1);
                    border: 1px solid rgba(238, 162, 60, 0.3);
                    color: #e6a23c;
                    border-radius: 4px;
                    height: 8px;
                    padding: 3px 5px;
                }

                .release-status {
                    padding: 0 5px;
                    border: 1px solid;
                    border-color: var(--color-release-status);
                    border-radius: 3px;
                    color: var(--color-release-status);
                    background-color: var(--color-release-status-rgba);
                }
            }
        }

        .klg-list {
            display: flex;
            max-width: 100%;
            flex-wrap: wrap;

            .k {
                width: 120px;
                height: 25px;
                padding: 5px;
                font-size: 12px;
                margin-right: 10px;
                border-radius: 3px;
                margin-bottom: 5px;
            }
        }

        .answer-exp {
            margin-bottom: 10px;
            font-size: 16px;
            overflow: hidden;
        }

        .tooltip {
            display: flex;
            justify-content: space-between;
            color: var(--color-deep);
            margin-top: 20px;
            font-size: 12px;

            .delete-text:hover {
                color: var(--color-primary);
            }

            .top-text:hover {
                font-weight: bold;
            }
        }
    }
}
</style>