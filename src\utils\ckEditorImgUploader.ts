import {http} from "@/apis";
class MyUploadAdapter {
    constructor(loader) {
        // @ts-ignore
        this.loader = loader
    }
    // 启动上载过程
    // upload() {
    //     const data = new FormData();
    //     data.append("upload",this.loader.file);
    //     uploadImage4CkEditor(data).then( res => {
    //         return {
    //             default:res.data.url
    //         }
    //     })
    // }
    async upload() {
        const data = new FormData();
        // @ts-ignores
        data.append("upload",await this.loader.file);
        const res = await http({
            url: '/welman/cos/file/imageCosUploadF', //给后端上传图片的接口
            method: 'post',
            data: data
        })
        return {
            default:res.data.url
        }
    }
    // 中止上载过程
    abort() {
        // @ts-ignore
        if (this.xhr) {
            // @ts-ignore
            this.xhr.abort()
        }
    }
}
export default MyUploadAdapter
