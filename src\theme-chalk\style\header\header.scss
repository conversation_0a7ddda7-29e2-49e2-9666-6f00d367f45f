@use '@/theme-chalk/mixins/mixins.scss' as *;

@include b(header) {
    box-sizing: border-box;
    height: $height__header;
    width: 100%;
    border-bottom: 5px solid rgb(242, 242, 242);

    // position
    position: fixed;
    z-index: 10;
    top: 0;
    background-color: white;

    // layout
    display: flex;
    justify-content: space-between;

    @include e(nav-left) {
        box-sizing: border-box;
        height: inherit;
        width: 200px;

        // layout
        display: flex;
        justify-content: space-evenly;
        align-items: center;

        //debug
    }

    @include e(logo-wrapper) {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        color: $color__primary;
    }

    @include e(logo-top) {
        font-size: $font-size__logo-top;
        font-family: $font-family-fangyuanti-semibold;
        font-weight: 600;
    }

    @include e(logo-bottom) {
        font-size: $font-size__logo-bottom;
        font-family: 'Microsoft YaHei';
    }

    @include e(title) {
        color: $color__primary;
        font-size: $font-size__logo-title;
        font-family: $font-family-puhuiti-L3;
        font-weight: 500;
    }

    @include e(nav-right) {
        display: flex;
    }

    @include e(item) {
        //debug
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        cursor: pointer;
    }

    @include e(icons-wrapper) {
        position: relative;
        width: 30px;

        img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    @include e(item-message) {
        font-family: $font-family-puhuiti-L3;
        font-size: 14px;
        color: $color__font-secondary--medium;
        font-weight: 400;

        &:hover {
            font-weight: bold;
        }
    }

    @include e(avatar-wrapper) {
        width: 150px;
        height: inherit;

        // layout

        display: flex;
        justify-content: center;
        align-items: center;

        // debug
    }

    @include e(avatar) {
        width: 35px;
        height: 35px;
        border-radius: 5px;
        background-color: blueviolet;
        cursor: pointer;

        &:hover {
        }
    }
}
