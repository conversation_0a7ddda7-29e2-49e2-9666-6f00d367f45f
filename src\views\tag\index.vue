<script setup lang="ts">
// 组件
import FormSwitch from "@/components/FormSwitch.vue"
import AddTagDialog from "./components/AddTagDialog.vue"
import MyFlipper from "@/components/MyFlipper.vue"
// 引入
import {
  type params2TagPrjList,
  type params2BindTag,
  getTagPrjListApi,
  bindTagListApi,
} from "@/apis/path/tagManage"
import { onMounted, ref } from "vue"
import { prjForm } from "@/utils/constant"
import { editingPrjStore } from "@/stores/editingPrj"
import type { tagType } from "@/utils/type"
import { ElMessage } from "element-plus"
import { processAllLatexEquations } from '@/utils/latexUtils';

// 定义
const tableData = ref([])
const curForm = ref("")
// const curState = ref(prjState.default.toString())
// const curType = ref(prjType.default.toString())
// const curTagList = ref<tagType[]>([])
const currentPage = ref(1)
const searchKey = ref("")
const pageSize = ref(10)
const total = ref(0)
const editingPrj = editingPrjStore()
const dialogRef = ref()
const params = ref<params2TagPrjList>({
  current: 1,
  limit: 10,
  tprjTitle: "",
  tprjForm: 0, // 视频:1 文稿:2 全部:0
})

// 函数
onMounted(() => {
  getNewPrjList(1)
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getNewPrjList(currentPage.value)
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  })
})

// 换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage
  getNewPrjList(newPage)
}
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

// 获取题目列表
const getNewPrjList = async (page: number) => {
  setParams(page)
  await getTagPrjListApi(params.value).then((res) => {
    // @ts-ignore
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total
    }
  })
}
// 设置params
const setParams = (page: number) => {
  params.value.current = page
  params.value.limit = 10
  params.value.tprjTitle = searchKey.value
  params.value.tprjForm = Number(curForm.value == "3" ? 0 : curForm.value)
}

// 处理选择=>formSwitch
const handleSelect = (form: prjForm) => {
  if (form !== prjForm.all) {
    curForm.value = form.toString()
  } else {
    curForm.value = ""
  }
  getNewPrjList(1)
}
// 处理搜索=>formSwitch
const handleSearch = (key: string) => {
  searchKey.value = key
  getNewPrjList(1)
}
//展示addTagDialog
const showTagDialog = (prj: object) => {
  const curTagList = ref<tagType[]>([])
  // @ts-ignore
  if (prj.tagList !== null) {
    // @ts-ignore
    prj.tagList.forEach(function (tag: tagType) {
      const curTag = ref<tagType>({
        areaCode: tag.areaCode,
        title: tag.title,
        choose: true,
      })
      curTagList.value.push(curTag.value)
    })
  }
 
  editingPrj.setPrjTagList(curTagList.value)
  // @ts-ignore
  if (prj.uniqueCode) {
    // @ts-ignore
    editingPrj.setPrjId(prj.uniqueCode)
  }
  dialogRef.value.showDialog("tag", 1)
}
// 刷新tag列表
const refreshTagList = (tList: tagType[], tForm: string) => {
  if (tForm == "tag") {
    const projectItem = tableData.value.find(
      (p) => p.uniqueCode === editingPrj.getPrjId()
    )
    if (projectItem) {
      const tagList2Bind = ref<params2BindTag>({
        uniqueCode: projectItem.uniqueCode,
        tagCodeList: [],
      })
      if (tList.length === 0) {
        projectItem.tagList = []
      } else {
        tList.forEach(function (tag: tagType) {
          projectItem.tagList = tList
          tagList2Bind.value.tagCodeList.push(tag.areaCode)
        })
      }

      bindTagListApi(tagList2Bind.value).then((res) => {
        if (res.success) {
          ElMessage.success("操作成功")
        }
      })
    }
  }
}
</script>

<template>
  <div class="main-container">
    <form-switch
      :needCurForm="true"
      :need-search="true"
      @select="handleSelect"
      @search="handleSearch"
      placeholder="请输入项目标题"
    ></form-switch>
    <div class="line"></div>
    <div class="content-container">
      <el-table
        ref="prjTable"
        :data="tableData"
        style="width: 100%; height: 100%; min-height: 550px"
        empty-text="暂无数据"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="100"
        >
        </el-table-column>
        <el-table-column
          class="notFixWidth"
          prop="tprjTitle"
          label="项目名称"
        >
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span class="prname ck-content" v-html="processAllLatexEquations(scope.row.tprjTitle)"></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="项目形式" width="100">
          <template #default="{ row }">
            {{ row.tprjForm == 2 ? "文稿" : "视频" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="releaseTime"
          label="发布时间"
          width="200"
        ></el-table-column>
        <el-table-column label="标签" width="350">
          <template #default="{ row }">
            <div class="taglist">
              <!-- 在这里使用 v-for 循环 row.tagList -->
              <el-tag
                v-for="(tag, index) in row.tagList"
                :key="index"
                type="primary"
              >
                {{ tag.title }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <span class="operationBlock">
              <!-- 使用枚举类可读性更高 -->
              <span @click="showTagDialog(row)" class="operationBtn"
                >维护标签</span
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <add-tag-dialog ref="dialogRef" @submit="refreshTagList"></add-tag-dialog>
</template>
<style scoped>
.main-container {
  margin: 20px auto auto auto;
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: 1300px;
  .line {
    position: relative;
    width: 100%;
    height: 1px;
    background-color: #ebeef5;
  }
  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 30px 30px;
    background-color: white;

    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 20px;

      .select-group {
        width: 390px;
        display: flex;
        justify-content: space-between;

        .select {
          width: 180px;

          &:deep(.el-input) {
            --el-input-height: 35px;
            line-height: 35px;
          }
        }
      }

      &::after {
        content: "";
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      }
    }

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
      .taglist {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 2px;
        margin-bottom: 2px;
      }
    }

    .operationBlock {
        display: inline;
        padding: 0 20px;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        color: var(--color-primary);
        .operationBtn {
          padding: 0 10px;
          &:hover {
            cursor: pointer;
            color: var(--color-primary);
            font-weight: 600;
            /* text-decoration: underline; */
          }
        }
        .disabledBtn {
          padding: 0 10px;
          color: var(--color-light);
          &:hover {
            cursor: no-drop;
          }
        }
      }
  }
}
</style>
