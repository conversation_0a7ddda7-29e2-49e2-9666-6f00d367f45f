import { http } from "@/apis";
import type { APIResponse, tagType } from "@/utils/type";

export interface param4mark {
  uniqueCode: string; // 项目Id
  chapterId: number; // 小节Id
  contentId?: number; // 内容Id
  keyword: string; // 关键字
  questionType: number; // 问题类型（1是什么/2为什么/3怎么做/4开放性问题）
  questionNecessity?: number; // 问题必要性（1.必须问题；2.参考问题）
  associatedWords: string; // 关联文本不能为空
  questionWeight?: number; // 个人问题或公开问题（1.个人问题；2.公开问题）
  questionDescription?: string; // 问题描述（开放性问题的描述）
}
// 划词
export function markWords4Text(param: param4mark): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/question/markWords",
    data: param,
  });
}
// 下面划词已经废弃
// // 测评类划词
// export function markWords4exam (param: param4mark): Promise<APIResponse> {
//     return http.request({
//         method: 'post',
//         url: '/twords/markWords',
//         data: param,
//     })
// }
// // 测评类划词
// export function markWords4video (param: param4mark): Promise<APIResponse> {
//     return http.request({
//         method: 'post',
//         url: '/tprjVideo/markWords',
//         data: param,
//     })
// }

// 划词获得问题列表
export function getWordApi(questionId: string): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/question/getMultiQuestion?questionId=${questionId}`,
  });
}

// 获取所有问题
export function getAllQuestionApi(
  chapterId: string,
  uniqueCode: string
): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/question/query?chapterId=${chapterId}&uniqueCode=${uniqueCode}`,
  });
}
