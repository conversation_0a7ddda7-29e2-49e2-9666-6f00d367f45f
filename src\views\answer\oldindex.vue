<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import 'element-plus/es/components/message/style/css';
import 'element-plus/es/components/message-box/style/css';
import FormSwitch from '@/components/FormSwitch.vue';
import MyFlipper from '@/components/MyFlipper.vue';
// import QuestionDrawer from "@/components/questionDrawer.vue"
import EditAnsDrawer from '@/components/EditAnsDrawer.vue';
import modeTypeSwitcher from '../prjdetail/components/modeTypeSwitcher.vue';

import { questionItem, answer } from '@/utils/type';
import { onMounted, ref, watch } from 'vue';
import {
  getAnswerListApi,
  addAnswerApi,
  deleteAnswerApi,
  publishAnswerApi,
  getAnswerDetailApi
} from '@/apis/path/answerList';
import type { params2getAnsList, params2AddAns, params2DelAns } from '@/apis/path/answerList';
import { ansMode, ansModeDict } from '@/utils/constant';
import { findKeyByValue } from '@/utils/func';
import { processAllLatexEquations } from '@/utils/latexUtils';

const tableData = ref([]); // 表格数据
const currentPage = ref(1); // 当前页
const pageSize = ref(10); // 每页大小
const total = ref(0); // 总数
const curHeaderMode = ref(0);
const answerTableRef = ref();
const answerDrawerRef = ref();
const params = ref<params2getAnsList>({
  type: curHeaderMode.value + 1,
  current: currentPage.value,
  limit: pageSize.value,
  answerStatus: ansMode.default,
  answerExplanation: '',
  keyword: ''
});

// 清除换行符等
const cleanHtml = (html: string) => {
  // console.log("[dbxx]", html.replace("<p>&nbsp;</p>", " "))
  return html.replace('<p>&nbsp;</p>', '');
};

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
// 获取答案列表
const getNewAnsList = (newPage?: number, ansExp?: string, prjTitle?: string, status?: number) => {
  if (newPage) params.value.current = newPage;
  params.value.answerExplanation = ansExp;
  params.value.keyword = prjTitle;
  if (status === 0 || status === -1 || status === 1 || status === 2) {
    params.value.answerStatus = status;
  }
  getAnswerListApi(params.value).then((res) => {
    if (res.success) {
      // TODO: 修改transToIcon
      tableData.value = res.data.list.map((item) => {
        if (item.stem) {
          return {
            ...item,
            answerExplanation: item.answerExplanation,
            stem: item.stem
          };
        } else {
          return {
            ...item,
            answerExplanation: item.answerExplanation
          };
        }
      });
      total.value = res.data.total;
      // console.log("[ans list res]",res.data)
    }
  });
};
// 处理选择 => formSwitch
const handleSelect = (status: number) => {
  getNewAnsList(1, params.value.answerExplanation, params.value.keyword, status);
};
// 处理搜索 => formSwitch
const handleSearchList = (ansKey: string, prjKey: string) => {
  getNewAnsList(1, ansKey, prjKey);
};
// 处理发布答案
const handlePublishAns = (ans: answer) => {
  if (ans.answerStatus !== ansMode.completed) return;
  publishAnswerApi(ans.answerId).then((res) => {
    if (res.success) {
      ElMessage.success('发布成功');
      getNewAnsList(currentPage.value);
    }
  });
};

// 处理编辑答案
const handleEditAns = (ans: any) => {
  getAnswerDetailApi(ans.answerId).then((res) => {
    if (res.success) {
      answerDrawerRef.value.showEditDrawer(ans, res.data.answer);
    } else {
      ElMessage.error('获取答案详情失败');
      getNewAnsList(currentPage.value);
    }
  });
};

// 处理删除答案
const handleDeleteAns = (ans: answer) => {
  if (ans.answerStatus === ansMode.processing) return;
  ElMessageBox.confirm('确定删除答案吗？', '删除答案', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = ref<params2DelAns>({
        answerId: ans.answerId
      });
      deleteAnswerApi(params.value).then((res) => {
        if (res.success) {
          getNewAnsList(currentPage.value);
          ElMessage.success('删除成功');
        } else {
          ElMessage.warning(res.message);
        }
      });
    })
    .catch();
};
// 处理编辑提交刷新列表
const handleEditSubmit = (success: boolean) => {
  if (success) {
    getNewAnsList(currentPage.value);
  }
};

// 换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getNewAnsList(newPage);
};
// 处理转换header的mode
const handleChangeHeader = (newHeaderMode: number) => {
  params.value.type = newHeaderMode + 1;
  curHeaderMode.value = newHeaderMode;
};
onMounted(() => {
  // getNewAnsList(currentPage.value)
});

watch(
  () => curHeaderMode,
  () => {
    getNewAnsList(currentPage.value);
  },
  { deep: true, immediate: true }
);
</script>
<template>
  <div class="answer-wrapper">
    <div class="header-container">
      <mode-type-switcher
        style="margin: 10px"
        :mode="curHeaderMode"
        @changeMode="handleChangeHeader"
      >
        <template v-slot:mode0> 项目的问题答案 </template>
        <template v-slot:mode1> 习题的问题答案 </template>
      </mode-type-switcher>
      <form-switch
        :need-ans-mode="true"
        :need-search-ans="true"
        :need-search-prj="true"
        @selectStatus="handleSelect"
        @searchList="handleSearchList"
        placeholder="请输入项目标题"
      ></form-switch>
    </div>
    <div class="line"></div>
    <div class="content-container">
      <el-table
        ref="answerTableRef"
        :data="tableData"
        class="table"
        empty-text="暂无数据"
        :row-style="{ height: '65px', overflow: 'hidden' }"
        :cell-style="{ height: '65px', width: '100%', overflow: 'hidden', maxHeight: '65px' }"
        :style="{ tableLayout: 'fixed' }"
      >
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" min-width="60">
        </el-table-column>
        <el-table-column
          class-name="ellipsis-column"
          prop="answerExplanation"
          label="答案解释"
          min-width="220"
        >
          <template #default="scope">
            <div class="ellipsis-cell">
              <el-tooltip placement="top" :show-after="200">
                <template #content>
                  <div
                    class="tooltip-content"
                    v-html="(scope.row.answerExplanation)"
                  ></div>
                </template>
                <div
                  class="ellipsis-text flex-start"
                  v-html="(scope.row.answerExplanation)"
                ></div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="klgNumber" label="已选知识数" min-width="95" align="center">
        </el-table-column>
        <el-table-column
          prop="taskNumber"
          label="待处理知识数"
          min-width="110"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="回答时间"
          width="110"
          align="center"
        ></el-table-column>
        <el-table-column
          label="所属问题"
          min-width="180"
          class-name="ellipsis-column"
          align="center"
        >
          <template #default="{ row }">
            <div>
              <el-tooltip placement="top" :show-after="200">
                <template #content>
                  <div class="tooltip-content">
                    <span v-html="(row.keyword)"></span>
                  </div>
                </template>
                <div class="flex-column">
                  <div class="flex">
                    <span class="quote-left">【</span>
                    <span
                      class="ellipsis-text"
                      v-html="(row.keyword)"
                      style="flex: 1; text-align: center"
                    ></span>
                    <span class="quote-right">】</span>
                  </div>
                  <div style="text-align: center; margin-top: 4px">
                    <b>{{ row.questionType }}</b>
                  </div>
                </div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="curHeaderMode === 0"
          label="所属项目"
          min-width="130"
          class-name="ellipsis-column"
        >
          <template #default="{ row }">
            <div>
              <el-tooltip placement="top" :show-after="200">
                <template #content>
                  <div
                    class="tooltip-content"
                    v-html="(row.prjTitle)"
                  ></div>
                </template>
                <div
                  class="content ck-content ellipsis-text"
                  v-html="(row.prjTitle)"
                ></div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-else label="所属题目" min-width="130" class-name="ellipsis-column">
          <template #default="{ row }">
            <div>
              <el-tooltip placement="top" :show-after="200">
                <template #content>
                  <div class="tooltip-content" v-html="(row.stem)"></div>
                </template>
                <div class="ellipsis-text" v-html="(row.stem)"></div>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="答案状态" min-width="80" align="center">
          <template #default="{ row }">
            {{ findKeyByValue(row.answerStatus, ansModeDict) as string }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="210" align="center">
          <template #default="{ row }">
            <span class="operationBlock">
              <span
                :class="row.answerStatus === ansMode.completed ? 'operationBtn' : 'disabledBtn'"
                @click="handlePublishAns(row)"
                >发布</span
              >
              <span class="operationBtn" @click="handleEditAns(row)">编辑</span>
              <span
                :class="row.answerStatus !== ansMode.processing ? 'operationBtn' : 'disabledBtn'"
                @click="handleDeleteAns(row)"
                >删除</span
              >
            </span>
            <!-- <span class="operation-block">
              <el-button
                @click="handlePublishAns(row)"
                key="primary"
                type="primary"
                text
                class="operation-btn"
                :disabled="row.answerStatus !== ansMode.completed"
              >
                发布
              </el-button>
              <el-button
                @click="handleEditAns(row)"
                key="primary"
                type="primary"
                text
                class="operation-btn"
              >
                编辑
              </el-button>
              <el-button
                @click="handleDeleteAns(row)"
                key="primary"
                type="primary"
                text
                class="operation-btn"
                :disabled="row.answerStatus === ansMode.processing"
              >
                删除
              </el-button>
            </span> -->
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
    <!-- 其他组件 -->
    <!-- 问题抽屉 -->
    <edit-ans-drawer @refresh="handleEditSubmit" ref="answerDrawerRef"></edit-ans-drawer>
  </div>
</template>
<style scoped>
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-invalid);
}

:deep(.el-table .el-table__cell) {
  height: 65px;
  overflow: hidden;
  width: 100%; /* 强制宽度为100% */
}

:deep(.el-table .el-table__cell div) {
  max-height: 65px;
  overflow: hidden;
  width: 100% !important; /* 强制宽度为100% */
}



.answer-wrapper {
  width: 1300px;
  .header-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: white;
    width: 100%;
  }
  .content-container {
    background-color: white;
    padding: 5px 30px 30px 30px;
    .table {
      width: 100%;
      height: 100%;
      table-layout: fixed; /* 强制表格使用固定布局 */
      .operationBlock {
        display: flex;
        width: 100%;
        padding: 0 20px;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        color: var(--color-primary);
        .operationBtn {
          padding: 0 10px;
          &:hover {
            cursor: pointer;
            color: var(--color-primary);
            font-weight: 600;
            /* text-decoration: underline; */
          }
        }
        .disabledBtn {
          padding: 0 10px;
          color: var(--color-light);
          &:hover {
            cursor: no-drop;
          }
        }
      }
      .content {
        display: inline-block;
        width: auto;
        min-width: 0;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-height: 65px;
      }

      .content p {
        display: inline-block;
        height: auto;
        margin: 0;
        padding: 0;
        max-height: 65px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      /* :deep(li){
        width: 100%;
        overflow:hidden;
      } */
      .operation-block {
        .operation-btn {
          margin-left: 0px;
        }
      }
    }
    .explantion {
      width: 100%;
      justify-content: flex-start;
      display: block;
      overflow-x: auto; /* 允许水平滚动 */
      overflow-y: visible; /* 允许垂直溢出 */
    }
  }
}
:deep(.explantion p) {
  display: inline-block;
  white-space: normal; /* 允许文本换行 */
  overflow: visible; /* 允许内容溢出 */
  max-width: 100%; /* 限制最大宽度 */
}
.line {
  position: relative;
  width: 100%;
  height: 1px;
  background-color: #ebeef5;
}
/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 5px; /* 滚动条宽度 */
  height: 6px; /* 滚动条高度 */
}

/* 左引号样式 */
.quote-left {
  flex-shrink: 0;
  margin-right: 4px; /* 增加右侧间距 */
  font-size: 16px; /* 调整引号大小 */
  line-height: 1;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2; /* 确保引号在渐变层上方 */
}

/* 右引号样式 */
.quote-right {
  flex-shrink: 0;
  margin-left: 4px; /* 增加左侧间距 */
  position: relative;
  z-index: 2; /* 确保引号在渐变层上方 */
  font-size: 16px; /* 调整引号大小 */
  line-height: 1;
  display: flex;
  align-items: center;
}
</style>
