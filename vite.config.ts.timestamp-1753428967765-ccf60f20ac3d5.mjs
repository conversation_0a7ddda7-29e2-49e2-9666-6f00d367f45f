// vite.config.ts
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/vite@5.3.5_@types+node@22.14.0_less@4.2.0_sass@1.77.6/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/@vitejs+plugin-vue@5.1.2_vite@5.3.5_@types+node@22.14.0_less@4.2.0_sass@1.77.6__vue@3.4.35_typescript@5.5.4_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { fileURLToPath, URL } from "node:url";
import AutoImport from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/unplugin-auto-import@19.0.0_@vueuse+core@9.13.0_vue@3.4.35_typescript@5.5.4___rollup@4.19.2/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/unplugin-vue-components@28.0.0_@babel+parser@7.25.3_rollup@4.19.2_vue@3.4.35_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/unplugin-vue-components@28.0.0_@babel+parser@7.25.3_rollup@4.19.2_vue@3.4.35_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/resolvers.js";
import mkcert from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/vite-plugin-mkcert@1.17.7_vite@5.3.5_@types+node@22.14.0_less@4.2.0_sass@1.77.6_/node_modules/vite-plugin-mkcert/dist/mkcert.mjs";
import { codeInspectorPlugin } from "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/node_modules/.pnpm/code-inspector-plugin@0.20.10/node_modules/code-inspector-plugin/dist/index.mjs";
var __vite_injected_original_import_meta_url = "file:///C:/Users/<USER>/Desktop/markdownDemo/dutchman_frontend/vite.config.ts";
var vite_config_default = defineConfig({
  plugins: [
    // commonjs() as any,
    vue(),
    AutoImport({
      imports: ["vue", "vue-router"],
      resolvers: [ElementPlusResolver({ importStyle: "sass" })]
    }),
    Components({
      resolvers: [ElementPlusResolver({ importStyle: "sass" })]
    }),
    mkcert(),
    codeInspectorPlugin({
      bundler: "vite"
    })
  ],
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern",
        additionalData: `@use "@/assets/theme/index.scss" as *;`
      }
    }
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
    }
  },
  server: {
    host: "0.0.0.0",
    port: 5675,
    proxy: {
      "/wellerman-service": {
        target: "http://**************:8001",
        changeOrigin: true
      },
      "/dutchman-service": {
        target: "http://**************:8001",
        // 测试环境调试
        changeOrigin: true
      },
      "/auth-service": {
        target: "http://**************:8001",
        // 测试环境调试
        changeOrigin: true
      }
    }
  }
  // optimizeDeps: {
  //   // include: ["ckeditor5-custom-build", "@ckeditor/ckeditor5-vue"],
  // },
  // build: {
  //   commonjsOptions: {
  //     // include: [/ckeditor-super/, /ckeditor-vue/, /node_modules/],
  //   },
  // },
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
