import { http } from "@/apis";
import type { APIResponse } from "@/utils/type";
interface page {
  current: number;
  limit: number;
}
export interface params2GetExerList extends page {
  type: string;
  stem: string;
}
export interface params2GetQuesList extends page {
  exerciseId: string;
  limit: number;
  current: number;
  questionStatus: number;
  questionType: string;
  questionNecessity: number;
  questionWeight: number;
  associatedWordsOrKeywords: string;
}

export interface params2MarkWord {
  exerciseId: string;
  keyword: string; // 关键字
  questionType: number; // 问题类型（1是什么/2为什么/3怎么做/4开放性问题）
  questionNecessity?: number; // 问题必要性（1.必须问题；2.参考问题）
  associatedWords: string; // 关联文本不能为空
  questionWeight?: number; // 个人问题或公开问题（1.个人问题；2.公开问题）
  questionDescription?: string; // 问题描述（开放性问题的描述）
}
// 获取习题列表
export function getExerciseListApi(
  params: params2GetExerList
): Promise<APIResponse> {
  return http.get(
    `/exercise/self/page?type=${params.type}&stem=${params.stem}&current=${params.current}&limit=${params.limit}`
  );
}
// 获取习题详情
export function getExerDetailApi(id: string): Promise<APIResponse> {
  return http.get(`/exercise/detail?exerciseId=${id}`);
}
// 获取分页问题列表
export function getExerQuesListApi(
  params: params2GetQuesList
): Promise<APIResponse> {
  return http.post(`/exercise/question/page`, params);
}
// 获取全量问题接口
export function getAllExerQuestionApi(id: string): Promise<APIResponse> {
  return http.get(`/exercise/question/all?exerciseId=${id}`);
}
// 划词提问
export function markWord4ExerciseApi(
  params: params2MarkWord
): Promise<APIResponse> {
  return http.post(`/exercise/markWords`, params);
}
