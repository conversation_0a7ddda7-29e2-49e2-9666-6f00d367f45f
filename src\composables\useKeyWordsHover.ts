/**
 * keyWords区域悬浮放大功能的核心逻辑
 * 现在由 App.vue 的 MutationObserver 统一调用
 */
let tooltipElement: HTMLElement | null = null;

export const showKeyWordsTooltip = (element: HTMLElement) => {
  hideKeyWordsTooltip();

  tooltipElement = document.createElement('div');
  const cloned = element.cloneNode(true) as HTMLElement;

  // 重置样式
  if (element.classList.contains('equation')) {
    cloned.style.cssText =
      'height: auto !important; margin: 0 !important; font-size: initial !important;';
  } else {
    cloned.style.cssText = 'height: auto !important; max-height: 200px; max-width: 300px;';
  }

  tooltipElement.appendChild(cloned);

  // 定位和样式
  const rect = element.getBoundingClientRect();
  Object.assign(tooltipElement.style, {
    position: 'fixed',
    top: `${rect.top - 10}px`,
    left: `${rect.left + rect.width / 2}px`,
    transform: 'translateX(-50%) translateY(-100%)',
    background: 'white',
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    zIndex: '9999',
    pointerEvents: 'none'
  });

  document.body.appendChild(tooltipElement);
};

export const hideKeyWordsTooltip = () => {
  if (tooltipElement && tooltipElement.parentNode) {
    tooltipElement.parentNode.removeChild(tooltipElement);
    tooltipElement = null;
  }
};

// 为单个元素绑定悬浮事件
export const bindKeyWordsHover = (element: HTMLElement) => {
  // 避免重复绑定
  if (element.hasAttribute('data-keywords-hover-bound')) {
    return;
  }

  element.addEventListener('mouseenter', () => {
    showKeyWordsTooltip(element);
  });

  element.addEventListener('mouseleave', () => {
    hideKeyWordsTooltip();
  });

  element.setAttribute('data-keywords-hover-bound', 'true');
};
