<script setup lang="ts">
// 该组件已废弃
import { onMounted, ref, watch, watchEffect } from "vue"
import MyButton from "@/components/MyButton.vue"
import MyTag from "@/components/MyTag.vue"
import type { klgType } from "@/utils/type"
import { getKlgListApi } from "@/apis/path/prjdetail"
import { ElMessage } from "element-plus"
import { Search } from "@element-plus/icons-vue"
import { qType } from "@/utils/constant"
const emits = defineEmits(["submitlen"])
const kList = ref<klgType[]>([])
const selectedKlgList = ref<klgType[]>([])
const loading = ref(false)

const maxWidth = 9
const isEllipsis = (data: string) => {
  const cleanedData = data.replace(/<[^>]*>/g, "")
  return cleanedData.length > maxWidth ? true : false
}
const props = defineProps({
  qType: Number, // 1: what other:other
})

// 处理选择
const handleSelect = (item: klgType) => {
  if (props.qType === qType.what) {
    if(selectedKlgList.value.length === 1){
      ElMessage.error("最多选择1个标签")
      return
    }
  }
  item.choose = !item.choose
  const foundItem = kList.value.find((i) => i.code === item.code)
  if (foundItem) {
    // 维护selectedKlgList
    const index = selectedKlgList.value.findIndex(
      (i: klgType) => i.code === item.code
    )
    if (index === -1) {
      selectedKlgList.value.push(item)
    } else {
      selectedKlgList.value.splice(index, 1)
    }
  }
}

// 处理删除tag
const handleDelete = (aimId: string) => {
  // 只维护selectedKlgList
  const aimItem = kList.value.find((k) => k.code == aimId)
  if (aimItem) {
    aimItem.choose = false
  }
  const index = selectedKlgList.value.findIndex(
    (i: klgType) => i.code === aimId
  )
  if (index !== -1) {
    selectedKlgList.value.splice(index, 1)
  }
}

// 筛选选项
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true
    getKlgListApi(query, props.qType).then((res) => {
      if(res.success) {
        loading.value = false
        kList.value = res.data.list
      }

    })
  } else {
    kList.value = []
  }
}
const cleanSelectedKlgList = () => {
  selectedKlgList.value.splice(0, selectedKlgList.value.length);
}
// 观察长度
watch(()=>selectedKlgList.value.length,(newvalue)=>{
  if(props.qType === qType.what) {
    emits("submitlen", newvalue)
  }
})

defineExpose({
  cleanSelectedKlgList,
  selectedKlgList,
})
</script>
<template>
  <div>
    <div class="content-container">
      <div class="search-bar">
        <el-select
          v-if="props.qType !== 1"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请输入知识名称"
          placement="top"
          :remote-method="remoteMethod"
          :loading="loading"
          collapse-tags
          :max-collapse-tags="0"
        >
          <el-option
            v-for="item in kList"
            :key="item.code"
            :label="item.title"
            :value="item.title"
            v-html="item.title"
            style="width: 100%"
            class="ck-content"
            @click="handleSelect(item)"
          />
        </el-select>
        <el-select
          v-else
          filterable
          remote
          reserve-keyword
          placement="top"
          placeholder="请输入知识名称"
          :remote-method="remoteMethod"
          :loading="loading"
        >
          <el-option
            v-for="item in kList"
            :key="item.code"
            :label="item.title"
            :value="item.title"
            v-html="item.title"
            style="width: 100%"
            class="ck-content"
            @click="handleSelect(item)"
          />
        </el-select>
      </div>
      <div class="selectedKlgList" id="bottom">
        sssssssssssssss
        <my-tag
          class="k"
          @delete="handleDelete"
          :tag-id="item.code"
          v-for="(item, index) in selectedKlgList"
          :key="index"
          :ellipsis="isEllipsis(item.title)"
        >
          <el-tooltip raw-content popper-class="tooltip-width">
            <template #content>
              <div class="tooltipHtml ck-content" v-html="item.title"></div>
            </template>
            <span class="htmlContent2 ck-content" v-html="item.title"></span>
          </el-tooltip>
        </my-tag>
      </div>
    </div>
  </div>
</template>

<style scoped>


.content-container {
  /*--el-color-primary: var(--color-primary);*/
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .search-bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 10px;
    width: 100%;
    .input {
      --el-color-primary: var(--el-border-color);
      width: 100%;

      .btn {
        cursor: pointer;
      }
    }
  }

  .kList {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 215px;
    overflow-y: auto;
    margin-bottom: 10px;
    width: 100%;
    border-top: 1px solid var(--color-line);
    border-bottom: 1px solid var(--color-line);
    padding: 10px 0px;

    .tItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      /*height: 35px;*/
      line-height: 35px;
      padding: 0 20px;
      border: 1px solid var(--color-boxborder);
      cursor: pointer;
      margin-bottom: 10px;

      &:hover,
      &.isSelected {
        background-color: var(--color-light);
      }
    }

    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
  }
  .selectedKlgList {
    /* background-color: blue; */
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;
    padding-top: 10px;

    .k {
      height: 25px;
      width: 160px;
      border-radius: 5px;
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 12px 12px 0;
      padding: 16px;
    }

    &::after {
      content: "";
      height: 1px;
      width: 94%;
      /* background-color: var(--color-line); */
      background-color: var(--color-line);
      position: absolute;
      top: 0;
    }
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>
