/// <reference types="vite/client" />

declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

declare module "vite" {
  export * from "vite";
}

declare module "@vitejs/plugin-vue" {
  const plugin: any;
  export default plugin;
}

declare module "unplugin-auto-import/vite" {
  const plugin: any;
  export default plugin;
}

declare module "unplugin-vue-components/vite" {
  const plugin: any;
  export default plugin;
}

declare module "unplugin-vue-components/resolvers" {
  export const ElementPlusResolver: any;
}

declare module "vite-plugin-mkcert" {
  const plugin: any;
  export default plugin;
}
