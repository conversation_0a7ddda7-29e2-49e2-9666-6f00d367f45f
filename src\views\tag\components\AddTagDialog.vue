<script setup lang="ts">
import { onMounted, ref } from "vue"
import MyButton from "@/components/MyButton.vue"
import MyTag from "@/components/MyTag.vue"
import { editingPrjStore } from "@/stores/editingPrj"
import type { tagType } from "@/utils/type"
import { getTagListApi, type params2tList } from "@/apis/path/tagManage"
import { ElMessage } from "element-plus"
import { Search } from "@element-plus/icons-vue"
const emit = defineEmits(["submit"])
const isDialogShow = ref(false)
const tForm = ref("") // tag | target
const dialogTitle = ref("")
const searchKey = ref("")
const current = ref(1)
const editingPrj = editingPrjStore()
const tList = ref<tagType[]>([])
const selectedTList = ref<tagType[]>([])
const limit = ref(-1) // 在step3的时候，限制选择条数
const total = ref(0)
const showSearchResult = ref(false)

const maxWidth = 9
// 检测是否省略 TODO: 修改逻辑，最好是判断渲染后的宽度
const isEllipsis = (data: string) => {
  // console.log("[data]", data);
  const cleanedData = data.replace(/<[^>]*>/g, "")
  // console.log("cleanedData", cleanedData ," = ",cleanedData.length);
  return cleanedData.length > maxWidth ? true : false
}
const showDialog = (
  form: string,
  step: number,
  maxLimit?: number,
  sTList?: tagType[]
) => {
  if (maxLimit != undefined) {
    limit.value = maxLimit
  }
  isDialogShow.value = true
  tForm.value = form
  dialogTitle.value = form === "tag" ? "添加标签" : "添加目标"
  // 数据初始化
  current.value = 1
  tList.value = []
  if (step == 1) {
    if (tForm.value == "tag") {
      selectedTList.value = [...editingPrj.getPrjTagList()]
    } else {
      selectedTList.value = [...editingPrj.getPrjTargetList()]
    }
  }
  if (sTList) {
    selectedTList.value = [...sTList]
  }
  if (limit.value != -1) {
    // 限制选择条数
    selectedTList.value = selectedTList.value.slice(0, limit.value)
  }
  // FIXME:目前标签开启了自动搜索，因为key为空时可以搜出来全部标签
  // 目标知识点搜索，key为空时搜不到东西，开了就是浪费通信资源
  // if (tForm.value == "tag") getTList()
}
const handleWheelFn = (e) => {
  // 如果已经是全部的标签，直接返回
  if (tList.value && tList.value.length >= total.value) return
  // @ts-ignore
  let bottom = document.querySelector("#bottom")?.offsetTop as number
  let now = document.querySelector("#scroll")?.scrollTop as number
  setTimeout(() => {
    if (now >= bottom && e.deltaY > 0) {
      // TODO: 暂未启用
      console.log("bottom! ")
    }
  }, 100)
}
const getTList = () => {
  // 和后端通信+洗数据
  // 1.保证tag和target数据结构一致（都弄成tagType类型）
  // 2.保证choose字段和值和前端暂存数据selectedTList一致
  // FIXME: 后端还没做分页
  let param = ref<params2tList>({
    current: current.value,
    limit: 5,
    keyword: searchKey.value,
    status: 1,
  })
  if (tForm.value === "tag") {
    getTagListApi(param.value)
      .then((res) => {
        // console.log("[res:getTAG]",res.data.list)
        // @ts-ignore
        if (res.success) {
          // 翻页：追加写
          // 如果后端改结构了，这里t的结构也要改
          if(res.data.list.length === 0) {
            showSearchResult.value = true
          }else{
            showSearchResult.value = false
          }
          let appendList: tagType[] = res.data.list?.map(
            (t: { areaCode: string; title: string; choose: boolean }) => {
              const matchedTag = selectedTList.value.find(
                (st: tagType) => st.areaCode === t.areaCode
              )
              if (matchedTag) {
                return {
                  areaCode: t.areaCode,
                  title: t.title,
                  choose: true,
                }
              } else {
                return {
                  areaCode: t.areaCode,
                  title: t.title,
                  choose: t.choose,
                }
              }
            }
          )

          tList.value.push(...appendList)
          // console.log(JSON.stringify(tList.value, null ,2))
        } else {
          ElMessage.error("加载标签列表异常")
        }
      })
      .catch()
  }
  current.value++
}

const handleSearch = async () => {
  // 数据初始化
  current.value = 1
  tList.value = []
  await getTList()
}

const handleSelect = (item: tagType) => {
  item.choose = !item.choose
  if (limit.value != -1 && selectedTList.value.length >= limit.value) {
    ElMessage.error("最多选择" + limit.value + "个标签")
    return
  }
  const foundItem = tList.value.find((i) => i.areaCode === item.areaCode)
  if (foundItem) {
    // 维护selectedTList
    const index = selectedTList.value.findIndex(
      (i: tagType) => i.areaCode === item.areaCode
    )
    if (index !== -1) {
      selectedTList.value.splice(index, 1)
    } else {
      selectedTList.value.push(item)
    }
  }
}
const handleDelete = (aimId: string) => {
  // 只维护selectedTList
  const aimItem = tList.value.find((t) => t.areaCode == aimId)
  if (aimItem) {
    aimItem.choose = false
  }
  const index = selectedTList.value.findIndex(
    (i: tagType) => i.areaCode === aimId
  )
  if (index !== -1) {
    selectedTList.value.splice(index, 1)
  }
}
const handleClose = () => {
  isDialogShow.value = false
}
const handleSubmit = () => {
  // console.log("[submit]:seleTlist",selectedTList.value, " tform ",tForm.value);
  emit("submit", selectedTList.value, tForm.value)
  isDialogShow.value = false
}
defineExpose({
  showDialog,
})

onMounted(() => {
  // getTList()
})
</script>

<template>
  <el-dialog :close-on-click-modal="false" v-model="isDialogShow" width="596">
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        {{ dialogTitle }}
      </div>
    </template>
    <div class="content-container">
      <div class="searchBar">
        <el-input
          class="input"
          v-model="searchKey"
          @keydown.enter="handleSearch()"
          :placeholder="'请输入目标名称'"
        >
          <template #suffix>
            <el-icon class="btn el-input__icon" @click="handleSearch()">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="tList" @wheel="handleWheelFn" id="scroll">
        <div v-if="showSearchResult" class="empty-list">
          <span>搜索结果为空</span>
        </div>
        <div
          class="tItem"
          :class="item.choose ? 'isSelected' : ''"
          v-for="item in tList"
          :key="item.areaCode"
          @click="handleSelect(item)"
        >
          <div class="htmlContent1 ck-content" v-html="item.title"></div>
        </div>
      </div>

      <div class="selectedTList" id="bottom">
        <my-tag
          class="t"
          @delete="handleDelete"
          :type="tForm"
          :tag-id="item.areaCode"
          v-for="item in selectedTList"
          :key="item.areaCode"
          :ellipsis="isEllipsis(item.title)"
        >
          <el-tooltip raw-content popper-class="tooltip-width">
            <template #content>
              <div class="tooltipHtml ck-content" v-html="item.title"></div>
            </template>
            <span class="htmlContent2 ck-content" v-html="item.title"></span>
          </el-tooltip>
        </my-tag>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}

.content-container {
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .searchBar {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 30px;
    width: 100%;

    .input {
      /* width: 536px; */
      width: 100%;
      .btn {
        cursor: pointer;
      }
    }
  }

  .tList {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 215px;
    overflow-y: auto;
    margin-bottom: 10px;
    width: 100%;
    .empty-list {
      display: flex;
      justify-content: center;
    }
    .tItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      /*height: 35px;*/
      line-height: 35px;
      padding: 0 20px;
      border: 1px solid var(--color-boxborder);
      cursor: pointer;
      margin-bottom: 10px;

      &:hover,
      &.isSelected {
        background-color: var(--color-light);
      }
    }

    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
  }
  .selectedTList {
    /* background-color: blue; */
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;
    padding-top: 10px;

    .t {
      margin: 0 10px 10px 0;
    }

    &::after {
      content: "";
      height: 1px;
      width: 94%;
      /* background-color: var(--color-line); */
      background-color: var(--color-line);
      position: absolute;
      top: 0;
    }
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>
