<template>
    <div class="answer-dialog-container">
        <el-dialog v-model="editDialogVisble" width="910">
            <template #header>
                <div class="header">
                    <span>编辑答案</span>
                </div>
            </template>
            <div class="main-wrapper">
                <el-form ref="ruleFormRef" :rules="rule4answer" :model="ruleForm">
                    <div class="top">
                        <div>
                            <span style="font-weight: 700;">{{ curQuestion.userName }}</span><span
                                style="margin-left: 10px;">的提问</span>
                        </div>
                        <div class="related-content-container" style="background-color: white; display: flex">
                            <b class="ques-mark">【</b><span style="max-width: 90%; overflow: hidden"
                                class="questionList markdown-content"
                                v-html="(curQuestion.keyword)"></span><b class="ques-mark">】</b>
                            <span class="q-type">{{ curQuestion.questionType }}?</span>
                        </div>
                        <div v-if="qTypeDict[curQuestion.questionType] === qType.open">
                            <span class="ques-decription">
                                <span class="ck-content" v-html="curQuestion.questionDescription"></span>
                            </span>
                        </div>
                        <el-form-item>
                            <div>
                                <span>{{ curQuestion.createTime }}</span>
                            </div>
                        </el-form-item>
                    </div>
                    <div class="middle">
                        <el-form-item>
                            <div style="margin-top: 20px;">
                                <span style="font-weight: 700;">{{ curAnswerDetail.answerer }}</span><span
                                    style="margin-left: 10px;">的回答</span>
                            </div>
                        </el-form-item>
                        <div class="answer-area">
                            <el-form-item prop="answer">
                                <div :class="qTypeDict[curQuestion.questionType] !== qType.open ? 'input-area' : 'input-area2'">
                                    <InlineEditor v-model="ruleForm.answer" style="width: 100%;" :height='500'
                                        :showToolbar="true">
                                    </InlineEditor>
                                </div>
                            </el-form-item>
                            <div class="klg-list">
                                <el-form-item v-if="qTypeDict[curQuestion.questionType] !== qType.open && isAddTagShow
                                    ">
                                    <div style="width: 100%; padding: 10px;">
                                        <span>请把答案中的知识点选出来:</span>
                                        <div style="margin-top: 10px">
                                            <el-select v-if="qTypeDict[curQuestion.questionType] !== qType.what" multiple
                                                filterable suffix-icon="Search" :fit-input-width="true" remote
                                                reserve-keyword placeholder="请输入知识名称" placement="top"
                                                :remote-method="remoteKlgMethod" :loading="loading" collapse-tags
                                                :remote-show-suffix="true" :max-collapse-tags="0">
                                                <template #suffix-icon>
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </template>
                                                <el-option v-for="item in kList" :key="item.code" :value="item.title"
                                                    :class="item.choose ? 'highlight' : ''" style="width: 100%"
                                                    @click="handleSelectKlg(item)">
                                                    <!-- 该实现出现了bug，导致后边的v-html不渲染 -->
                                                    <div style="display: inline">
                                                        <span class="option-tag" :class="`option-tag${item.type}`">{{
                                                            item.type
                                                            ===
                                                            2 ? "领域" : "知识" }}
                                                        </span>
                                                    </div>
                                                    <div style="display: inline">
                                                        <span class="ck-content" v-html="item.title" id="p_inline"></span>
                                                    </div>
                                                </el-option>
                                            </el-select>
                                            <el-select v-else filterable :fit-input-width="true" suffix-icon="Search" remote
                                                reserve-keyword placeholder="请输入知识名称" placement="top"
                                                :remote-method="remoteKlgMethod" :remote-show-suffix="true"
                                                :loading="loading">
                                                <template #suffix-icon>
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </template>
                                                <el-option v-for="item in kList" :key="item.code" :label="item.title"
                                                    :value="item.title" :class="item.choose ? 'highlight' : ''"
                                                    style="width: 100%" @click="handleSelectKlg(item)">
                                                    <span class="option-tag" :class="`option-tag${item.type}`">{{ item.type
                                                        ===
                                                        2 ?
                                                        "领域" : "知识" }}</span>
                                                    <span class="ck-content" v-html="item.title"></span>
                                                </el-option>
                                            </el-select>
                                        </div>
                                        <div class="selected-list" id="bottom">
                                            <my-tag class="klg" @delete="handleDelete" :tag-id="item.code"
                                                v-for="item in curAnswerKlg" :key="item.code" type="newTarget">
                                                <el-tooltip raw-content popper-class="tooltip-width">
                                                    <template #content>
                                                        <div class="tooltipHtml ck-content" v-html="item.title"></div>
                                                    </template>
                                                    <span class="htmlContent2 ck-content" v-html="item.title"></span>
                                                </el-tooltip>
                                            </my-tag>
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item v-if="isAddTagShow &&
                                    isAddTaskShow &&
                                    qTypeDict[curQuestion.questionType] !== qType.open
                                    ">
                                    <div class="line"></div>
                                </el-form-item>
                            </div>
                        </div>
                    </div>
                    <div class="bottom">
                        <el-form-item v-if="qTypeDict[curQuestion.questionType] !== qType.open &&
                            isAddTaskShow
                            ">
                            <div style="width: 100%">
                                <div class="add-klg" style="width: 100%">
                                    <span> 如果没有找到合适的知识点,请点这里 </span>
                                    <span class="icon" @click="handleAddTask">
                                        <img style="margin-top: 4px" src="@/assets/images/answerlist/u736.svg" />
                                    </span>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item v-if="qTypeDict[curQuestion.questionType] !== qType.open &&
                            isAddTaskShow
                            " v-for="(item, index) in ruleForm.list" :key="item.areaCode">
                            <div v-if="!item.deleted" class="task-list">
                                <el-col :span="16">
                                    <el-form-item :prop="`list.${index}.klgName`" :rules="{
                                        required: true,
                                        message: '请输入任务名称',
                                        trigger: 'blur',
                                    }" style="width: 100%">
                                        <!-- 编辑模式：显示输入框 -->
                                        <el-input
                                            v-if="!item.showPreview"
                                            v-model="item.klgName"
                                            style="width: 100%; margin-right: 10px"
                                            placeholder="请输入任务名称，支持数学公式"
                                            maxlength="128"
                                            show-word-limit
                                            @blur="item.showPreview = true"
                                            @focus="item.showPreview = false"
                                        />

                                        <!-- 预览模式：显示渲染结果 -->
                                        <div
                                            v-else-if="item.klgName && item.showPreview"
                                            class="preview-container"
                                            style="width: 100%; margin-right: 10px"
                                            @click="item.showPreview = false"
                                        >
                                            <div
                                                class="preview flex-center"
                                                v-html="convertMathFormulas(item.klgName)"
                                            ></div>
                                        </div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item :prop="`list.${index}.areaTitle`" :rules="{
                                        required: true,
                                        message: '请选择领域',
                                        trigger: 'blur',
                                    }" style="width: 100%">
                                        <el-select v-model="item.areaCode" filterable :fit-input-width="true"
                                            suffix-icon="Search" clearable remote reserve-keyword placeholder="选择领域"
                                            :remote-method="remoteMethod" :remote-show-suffix="true" :loading="loading"
                                            :validate-event="true" style="width: 100%">
                                            <template #suffix-icon>
                                                <el-icon>
                                                    <Search />
                                                </el-icon>
                                            </template>
                                            <template #label>
                                                <span class="ck-content" v-html="item.areaTitle"></span>
                                            </template>
                                            <el-option v-for="tag in tagList" :key="tag.title" :label="tag.title"
                                                :value="tag.areaCode" v-html="tag.title" class="ck-content"
                                                :class="item.areaCode === tag.areaCode ? 'highlight' : ''"
                                                style="width: 100%" @click="handleSelectTag(tag, item)" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <span class="icon" @click="handleDelTask(item)" v-if="item.taskStatus==1 || item.taskStatus==3 || item.taskStatus==4 || item.taskStatus==5">
                                    <img src="@/assets/images/answerlist/u742.svg" />
                                </span>
                            </div>
                            <div v-if="item.taskStatus !== null && !item.deleted
                                " style="width: 100%">
                                <div class="feedback-content" :class="item.taskStyle" v-if="item.taskStatus">
                                    <div class="status">
                                        <b>{{
                                            findKeyByValue(
                                                item.taskStatus,
                                                taskCompleteTypeDict
                                            ) as string
                                        }}</b>
                                    </div>
                                    <div v-if="item.taskStatus === 2">
                                        <div class="handler">
                                            {{ item.handlerName }}
                                        </div>
                                    </div>
                                    <div v-else-if="item.taskStatus === 3 ||
                                        item.taskStatus === 4 ||
                                        item.taskStatus === 6
                                        ">
                                        <div class="handler">
                                            {{ item.handlerName }}
                                        </div>
                                        <div class="klg">
                                            <span class="ck-content"
                                                v-html="(item.klgTitle)"></span>
                                        </div>
                                    </div>
                                    <div v-else-if="item.taskStatus === 5">
                                        <div class="handler">
                                            {{ item.handlerName }}
                                        </div>
                                        <div class="klg">
                                            {{ item.feedback }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleConfirm" style="width: 120px; height: 35px;">
                        提交
                    </el-button>
                    <el-button @click="handleClose" style="width: 120px; height: 35px;">取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { addAnswerApi, editAnswerApi, getAnswerDetailApi, params2AddAns, params2EditAns } from '@/apis/path/answerList';
import { useDialogStore } from '@/stores/dialog';
import { answerItem, klgType, questionDetail, questionItem, tagType, taskType, taskTypePlus } from '@/utils/type';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { computed, onMounted, reactive } from 'vue';
import { ref, watch } from 'vue';
import InlineEditor from "@/components/editors/VeditorInline.vue";
import { getAnswersApi, getKlgListApi } from '@/apis/path/prjdetail';
import { qMode, qType, qTypeDict, qWeight, taskCompleteTypeDict } from "@/utils/constant";
import { getTagListApi, params2tList } from '@/apis/path/tagManage';
import { processAllLatexEquations, convertMathFormulas } from '@/utils/latexUtils';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { findKeyByValue } from "@/utils/func";

const emits = defineEmits(["edit", "add", "openCnt", "refresh"]);
const dialogStore = useDialogStore()
const editDialogVisble = ref(dialogStore.editDialogVisible)
const answerId = ref(dialogStore.answerId)
const questionId = ref(dialogStore.questionId)

const curQuestion = reactive<questionItem>({
    questionId: -1,
    questionDescription: '',
    associatedWords: '',
    keyword: '',
    questionType: '是什么',
    questionNecessity: qMode.ness,
    questionWeight: qWeight.open,
    userName: '',
    createTime: '',
    answerNumber: 0,
    questionState: '',
    canDelete: true
});

const curAnswerDetail = reactive<questionDetail>({
    stem: "",
    questioner: "",
    answerer: "",
    answerExplanation: "",
    answerKlgs: [],
    createTime: "",
    answerStatusList: [],
});

const pageSize = ref(10);
const currentPage = ref(1);

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
    answer: "",
    list: [],
});

const curAnswerKlg = ref<klgType[]>([]);

const kList = ref([]);
const loading = ref(false);

const tagList = ref<tagType[]>([]);

// 计算未删除的任务数量
const tasksLength = computed(() => {
    return ruleForm.list.filter((task) => !task.deleted).length;
});

// 处理添加问题
const handleAddTask = () => {
    if (
        qTypeDict[curQuestion.questionType] === qType.what &&
        tasksLength.value === 1
    ) {
        ElMessage.info("最多只能添加一条");
        return;
    }
    const task = ref<taskTypePlus>({
        klgName: "",
        areaTitle: "",
        areaCode: "",
        taskStatus: 0,
        handlerName: "",
        feedback: null,
        oid: null,
        taskStyle: "",
        deleted: false,
    });
    ruleForm.list.push(task.value);
};

const taglength = computed(() => curAnswerKlg.value.length)
const tasklength = computed(() => tasksLength.value)

const isAddTagShow = computed(() => {
    if (qTypeDict[curQuestion.questionType] == qType.what) {
        const totalLength = taglength.value + tasklength.value
        if (totalLength === 1) {
            return taglength.value > 0
        } else {
            return true
        }
    } else if (qType[curQuestion.questionType] === qType.open) {
        return false;
    } else {
        return true;
    }
})

const isAddTaskShow = computed(() => {
    if (qTypeDict[curQuestion.questionType] == qType.what) {
        const totalLength = taglength.value + tasklength.value
        if (totalLength === 1) {
            return tasklength.value > 0
        } else {
            return true
        }
    } else if (qType[curQuestion.questionType] === qType.open) {
        return false;
    } else {
        return true;
    }
})

watch(
    () => dialogStore.editDialogVisible,
    (newVal: boolean) => {
        editDialogVisble.value = newVal;
    },
    { immediate: true } // 立即执行一次以初始化值
);

watch(
    editDialogVisble,
    (newVal) => {
        if (newVal !== dialogStore.editDialogVisible) {
            dialogStore.editDialogVisible = newVal;
        }
    }
);

const generateKlgString = () => {
    return curAnswerKlg.value.map((item) => item.code).join("@@");
};

// 处理编辑答案
const handleEditAnsSubmit = () => {
    ruleFormRef.value.validate((valid) => {
        if (valid) {
            const toEditTaskList = ref([]);
            toEditTaskList.value = ruleForm.list.map((task) => {
                return {
                    oid: task.oid ? task.oid : null,
                    klgName: task.klgName,
                    areaTitle: task.areaTitle,
                    areaCode: task.areaCode,
                    deleted: task.deleted ? task.deleted : false,
                };
            });
            const params = ref<params2EditAns>({
                answerId: answerId.value,
                answerKlgs: generateKlgString(),
                answerExplanation: ruleForm.answer,
                taskList: toEditTaskList.value,
            });
            editAnswerApi(params.value).then((res) => {
                if (res.success) {
                    emits("refresh", true);
                    dialogStore.refresh = true
                    ElMessage.success("编辑成功！");
                    handleClose();
                }
            });
        } else {
        }
    });
};

// 处理选择klg
const handleSelectKlg = (item: klgType) => {
    if (qTypeDict[curQuestion.questionType] === qType.what) {
        if (curAnswerKlg.value.length === 1) {
            ElMessage.error("最多选择1个标签");
            return;
        }
    }
    // 基于code判断是否已存在
    const existingIndex = curAnswerKlg.value.findIndex(i => i.code === item.code);
    if (existingIndex === -1) {
        // 添加新对象
        curAnswerKlg.value.push({ ...item, choose: true }); // 确保choose属性正确
    } else {
        // 移除已存在的对象（仅当用户再次点击时）
        curAnswerKlg.value.splice(existingIndex, 1);
    }
};

const remoteKlgMethod = (query: string) => {
    if (query) {
        loading.value = true;
        getKlgListApi(query, qTypeDict[curQuestion.questionType]).then((res) => {
            if (res.success) {
                loading.value = false;
                kList.value = res.data;
            }
        });
    } else {
        kList.value = [];
    }
};

const handleDelete = (aimId: string) => {
    const newKlg: klgType[] = curAnswerKlg.value.filter(
        (item) => item.code != aimId
    );
    curAnswerKlg.value = newKlg;
};

const rule4answer = reactive<FormRules>({
    answer: [{ required: true, message: "请输入答案", trigger: "blur" }],
    list: [],
});

// 筛选选项
const remoteMethod = (query: string) => {
    if (query) {
        loading.value = true;
        const params = ref<params2tList>({
            limit: pageSize.value,
            current: currentPage.value,
            keyword: query,
            status: 1,
        });
        getTagListApi(params.value).then((res) => {
            // @ts-ignore
            if (res.success) {
                loading.value = false;
                tagList.value = res.data.list;
            }
        });
    } else {
        tagList.value = [];
    }
};

// 处理选择tag
const handleSelectTag = (tag: tagType, item: taskTypePlus) => {
    item.areaCode = tag.areaCode;
    item.areaTitle = tag.title;
};

// 处理删除任务
const handleDelTask = (task: any) => {
    // 使用findIndex找到task在数组中的索引
    const index = ruleForm.list.findIndex(
        (t) => t.areaTitle === task.areaTitle && t.klgName === task.klgName
    );
    // 如果找到了任务，就使用splice方法删除它
    if (index !== -1) {
        if (task.oid) {
            task.deleted = true;
        } else {
            ruleForm.list.splice(index, 1);
        }
    }
};

const handleClose = () => {
    curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
    ruleForm.list.splice(0, ruleForm.list.length);
    ruleForm.answer = "";
    editDialogVisble.value = false;
};

const handleConfirm = () => {
    handleEditAnsSubmit()
};

onMounted(async () => {
    const res: any = await getAnswersApi(questionId.value)
    if (res.success) {
        const data = res.data.questionAndAnswer[0]
        curQuestion.userName = data.userName
        curQuestion.questionType = data.questionType
        curQuestion.keyword = data.keyword
        curQuestion.questionNecessity = data.questionNecessity
        curQuestion.questionWeight = data.questionWeight
        curQuestion.createTime = data.createTime
        curQuestion.questionState = data.questionState
        curQuestion.questionId = data.questionId
        curQuestion.questionDescription = data.questionDescription
        curQuestion.canDelete = data.canDelete
        curQuestion.associatedWords = data.associatedWords
        curQuestion.answerNumber = data.answerNumber
    }

    const res2: any = await getAnswerDetailApi(answerId.value)
    if (res2.success) {
        const detail = res2.data.answer
        Object.assign(curAnswerDetail, detail);
        ruleForm.answer = curAnswerDetail.answerExplanation;
        if (detail.answerKlgs) {
            curAnswerKlg.value = (detail.answerKlgs).map(item => ({
                ...item,
                choose: true, // 确保初始化数据有choose属性
                code: item.klgCode?.trim() || '',
            }));
        }
        ruleForm.list = curAnswerDetail.answerStatusList.map((task) => {
            return {
                ...task,
                taskStyle: "feedback-style" + task.taskStatus,
                oid: task.oid ? task.oid : null,
                deleted: false,
            };
        });
        console.log(ruleForm.list)
    }
})
</script>

<style scoped lang="less">
:deep(.el-form-item) {
    margin-bottom: 0;
}

.header {
    width: 880px;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
}

.main-wrapper {
    width: 850px;
    margin: 0 auto;

    .top {
        border-bottom: 1px solid #dcdfe6;

        .related-content-container {
            display: flex;
            align-items: center;
            margin: 12px 0;
            background-color: var(--color-light);
            word-break: break-all;

            .ques-decription {
                display: flex;
                text-align: center;
                min-width: 100%;
                border: 1px solid var(--color-primary);
                border-radius: 5px;
                padding: 10px;
            }

            .ques-username {
                margin-right: 10px;
            }

            .ques-mark {
                display: flex;
                align-items: center;
                font-size: 14px;
                font-weight: 600;
                color: #333333;
            }

            .questionList {
                font-size: 14px;
                font-weight: 600;
                color: #333333;
            }

            .q-type {
                display: inline-block;
                font-weight: bold;
                //padding-left: 10px;
                white-space: nowrap;
            }
        }
    }

    .middle {
        width: 100%;

        .answer-area {
            //margin-top: 20px;
            width: 100%;
            display: flex;
            gap: 10px;
            margin-bottom: 18px;

            .input-area {
                width: 490px;
                height: 500px;
            }

            .input-area2 {
                width: 830px;
                height: 500px;
            }

            .klg-list {
                width: 340px;
                height: 500px;

                .option-tag {
                    margin-right: 10px;
                    border: 1px solid black;
                    padding: 0 5px;
                    border-radius: 3px;
                    font-weight: 400 !important;
                }

                .selected-list {
                    display: flex;
                    flex-wrap: wrap;
                    margin-top: 10px;
                    width: 340px;
                    max-height: 400px;
                    overflow: auto;

                    .klg {
                        width: 300px;
                        padding-left: 10px;
                        margin-bottom: 5px;
                        border-radius: 4px;

                        .htmlContent2 {
                            display: inline-block;
                            max-width: 280px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            vertical-align: middle;
                        }
                    }
                }
            }
        }
    }

    .bottom {
        border-top: 1px solid #dcdfe6;
        width: 100%;

        .task-list {
            width: 100%;
            display: flex;
            //margin-bottom: 10px;
            justify-content: space-between;

            .select-area {
                margin-left: 10px;
                width: 35%;
            }

            .icon {
                margin-left: 10px;
                display: flex;
                align-items: center;

                &:hover {
                    cursor: pointer;
                }
            }
        }

        .feedback-content {
            width: 100%;
            border-radius: 3px;
            padding: 5px 15px;
            margin-bottom: 10px;

            .status {
                font-size: 14px;
            }

            .handler {
                font-size: 12px;
            }

            .klg {
                font-size: 14px;
            }
        }

        .feedback-style1 {
            color: var(--color-assigned);
            background-color: var(--color-assigned-rgba);
        }

        .feedback-style2 {
            color: var(--color-assigned);
            background-color: var(--color-assigned-rgba);
        }

        .feedback-style3 {
            color: var(--color-completed);
            background-color: var(--color-completed-rgba);
        }

        .feedback-style4 {
            color: var(--color-completed);
            background-color: var(--color-completed-rgba);
        }

        .feedback-style6 {
            color: var(--color-primary);
            background-color: var(--color-primary-rgba);
        }

        .feedback-style5 {
            color: var(--color-returned);
            background-color: var(--color-returned-rgba);
        }
    }
}

.dialog-footer {
    text-align: center;
}

.preview-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 12px;
    background-color: #ffffff;
    cursor: pointer;
    height: 32px;
    display: flex;
    align-items: center;
    
    .preview {
        line-height: 1.4;
        color: #606266;
        width: 100%;
    }
    
    &:hover {
        border-color: #c0c4cc;
    }
}
</style>