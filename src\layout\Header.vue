<script setup lang="ts">
import { onMounted, ref } from "vue"
import { ElMessage } from "element-plus"
import { userInfoStore } from "@/stores/userInfo"
import { routerPushSystem } from "@/utils/jump"
import { System } from "@/enums/system"
import { logoutApi } from "@/apis/path/userinfo"

const userinfo = userInfoStore()
// 个人空间
const perurl = import.meta.env.VITE_APP_YOUTH_URL + "/userspace"

const learningHistory = import.meta.env.VITE_APP_CHAMP_URL + '/learnStatistics'
// 账号资料
const accurl = import.meta.env.VITE_APP_YOUTH_URL + "/user/userinformation"
// 后台管理
const backurl = import.meta.env.VITE_APP_ADMIN_URL + '/welcome'

const backendManagement = ref(false)

const avatar = userinfo.getAvatar()
const permissions = userinfo.getPermission()
const admin = permissions.find((system) => system.service === "admin")
if (admin && admin.access) {
  backendManagement.value = true
}

// 登出
const logout = async () => {
  try {
    const res = await logoutApi()
    if (res.success) {
      ElMessage.success("退出成功")
    }
  } catch (error) {
    console.error(error)
  }
}
// 跳转
const routerPush = (targetRoute: string) => {
  if (targetRoute === undefined) {
    ElMessage.error("路由跳转失败")
  } else {
    setTimeout(() => {
      window.open(targetRoute, "_self")
    }, 0)
  }
}
</script>

<template>
  <div class="header">
    <span class="left-wrapper">
      <span class="logo">
        <img class="pic" src="@/assets/logo.png" style="height: 60px" />
      </span>
      <span class="title">
        <span>问题中心</span>
      </span>
      <span class="pusher">
        <span class="pusher-item" @click="routerPushSystem(System.champaign)">
          <!-- TODO: 这个地方的图标没法从原型上获取（俩东西拼一起的 -->
          <img class="pusher-item-img" src="@/assets/study.svg" alt="" />
          <span>学习站</span>
        </span>
      </span>
    </span>
    <span class="avatar-wrapper">
      <el-dropdown>
        <img class="avatar" :src="avatar" style="width: 35px" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="routerPush(perurl)"
              >个人空间</el-dropdown-item
            >
            <el-dropdown-item @click="routerPush(learningHistory)"
              >学习历史</el-dropdown-item
            >
            <el-dropdown-item
              @click="routerPush(accurl)"
              >账号资料</el-dropdown-item
            >
            <el-dropdown-item
              v-if="backendManagement"
              @click="routerPush(backurl)"
              >后台管理</el-dropdown-item
            >
            <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </span>
  </div>
</template>

<style scoped lang="less">
.header {
  height: 60px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  background-color: white;
  /*position: relative;*/
  /*z-index: 3000;*/
  .left-wrapper {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    height: 100%;
    font-family: var(--font-family-logo);
    color: var(--color-primary);

    .logo {
      width: 106px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      /*padding: 10px 0;*/
      .pic {
        width: 60px;
      }

      // .chi {
      //   display: inline-block;
      //   line-height: 24px;
      //   font-size: 24px;
      //   font-weight: 600;
      //   font-family: "Alimama FangYuanTi VF SemiBold",
      //     "Alimama FangYuanTi VF Regular", "Alimama FangYuanTi VF", sans-serif;
      //   margin-bottom: 6px;
      // }

      // .eng {
      //   display: inline-block;
      //   line-height: 10px;
      //   font-size: 10px;
      // }
    }

    .title {
      font-size: 20px;
      font-weight: 600;
      color: var(--color-primary);
    }
    .pusher {
      display: flex;
      flex-direction: row;
      height: 100%;
      align-items: center;
      justify-content: center;
      margin-left: 30px;
      .pusher-item {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-deep);
        font-size: 14px;
        margin-left: 40px;
        &:hover {
          font-weight: 600;
          cursor: pointer;
        }
        .pusher-item-img {
          margin-right: 5px;
        }
      }
    }
  }

  .avatar-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    font-family: var(--font-family-text);

    .avatar {
      margin-left: 79px;
      margin-right: 65px;
      border-radius: 10px;
      height: 35px;
      width: 35px;
      cursor: pointer;

      &:focus-visible {
        outline: none !important;
      }
    }
  }
}
</style>
