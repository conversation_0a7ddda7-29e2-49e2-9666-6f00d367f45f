import { createAxiosByinterceptors } from "./request"
import type { AxiosInstance } from "axios"

let http: AxiosInstance = null
let http_info: AxiosInstance = null
let http_auth: AxiosInstance = null

;(function () {
  http = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_QUES_API_BASE,
  })
  http_info = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_INFO_API_BASE,
  })
  http_auth = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_AUTH_API_BASE,
  })
})()

export { http, http_info, http_auth }
