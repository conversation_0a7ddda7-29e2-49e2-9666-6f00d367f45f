import type { ChildNode, Document, Text } from 'domhandler';
import { ElementType, parseDocument } from 'htmlparser2';
import { trim } from 'lodash-es';
import URLParse from 'url-parse';
import { escapeRegExp } from 'lodash-es';
import { getVisibleLatex } from './getVisibleLatex';
import katex from 'katex';
import { decode, decodeHTML } from 'entities';
import * as he from 'he';

export function buildRegSubString(htmlString: string) {
  const dom = parseDocument(htmlString, {
    decodeEntities: true
  });
  let regSubString = '';
  const dfs = (node: ChildNode) => {
    if (node.type == ElementType.Text) {
      // regSubString += trim(node.data.replace(/\n/g, '').replace(/\r/g, '').replace(/\t/g, ''));
      regSubString += node.data;
      length += node.data.length;
    } else if (node.type == ElementType.Script) {
      const code = (node.firstChild as Text).data;
      const outerHTML = katex.renderToString(code, {
        throwOnError: false,
        output: 'html'
      });
      const key = getVisibleLatex(outerHTML);
      regSubString += key;
      length += 1;
    } else if (node.type == ElementType.Tag) {
      if (node.tagName == 'img') {
        const src = node.attribs.src;
        const key = URLParse(src).pathname.split('/').pop() as string;
        regSubString += key;
        console.log("key2", key)
        length += 1;
        // console.log("subString", regSubString)
        // console.assert(uncommonWordMap.has(key), `出现了很奇怪的图片: ${key}`);
        // regSubString += uncommonWordMap.get(key);
      } else {
        for (const child of node.children) {
          dfs(child);
        }
      }
    } else {
      for (const child of (node as Document).children) {
        dfs(child);
      }
    }
  };
  dfs(dom);
  console.log("subString", regSubString)
  return trim(regSubString);
}

export function findAllOccurrences(str: string, subStr: string) {
  // console.log('matchStr:', matchStr);
  // console.log('matchSubStr:', matchSubStr);
  const regex = new RegExp(escapeRegExp(subStr), 'gi');
  const matches = [...str.matchAll(regex)];
  const positions = matches.map((match) => match.index);
  return positions;
}
