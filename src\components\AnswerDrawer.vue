<script setup lang="ts">
/**
 * 查看问题模块
 */
import { computed, inject, nextTick, onMounted, onUnmounted, reactive, Ref, ref, watch } from 'vue';
import { qMode, qType, qTypeDict, qWeight } from '@/utils/constant';
import type {
  questionItem,
  questionDetail,
  klgType,
  taskType,
  tagType,
  answerItem,
  taskTypePlus
} from '@/utils/type';
import MyButton from '@/components/MyButton.vue';
import MyTag from './MyTag.vue';
import { ElFormItem, ElMessage, ElMessageBox } from 'element-plus';
import InlineEditor from '@/components/editors/VeditorInline.vue';
import type { FormInstance, FormRules } from 'element-plus';
import {
  deleteQuestionApi,
  getAnswersApi,
  getKlgListApi,
  params2DelQues
} from '@/apis/path/prjdetail';
import { getTagListApi, type params2tList } from '@/apis/path/tagManage';
import { addAnswerApi, deleteAnswerApi, TopAnswerApi } from '@/apis/path/answerList';
import type { params2AddAns, params2DelAns, params2TopAns } from '@/apis/path/answerList';
import { userInfoStore } from '@/stores/userInfo';
import router from '@/router';
import { useRoute } from 'vue-router';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { useRouterPushStore } from '@/stores/routerPushStore';
import { findKeyByValue } from '@/utils/func';
import { renderMarkdown } from '@/utils/markdown';
import { processAllLatexEquations, convertMathFormulas } from '@/utils/latexUtils';
import { useDialogStore } from '@/stores/dialog';
import { CaretBottom, CaretTop } from '@element-plus/icons-vue';

const userinfo = userInfoStore();
const routerPushStore = useRouterPushStore();
const dialogStore = useDialogStore();

const answerItemList = ['answer-item', 'answer-item2'];
const loading = ref(false);
const isDrawerShow = ref(false); // 是否展示抽屉
const drawerTitle = ref(); // 抽屉名
const ruleFormRef = ref<FormInstance>(); //
const curMode = ref(-1); // 0新增|1编辑|2只读
const tagList = ref<tagType[]>([]);
const kList = ref([]);
const answerList = ref([]);
const isAddTaskShow = ref<boolean>(true);
const isAddTagShow = ref<boolean>(true);
const currentPage = ref(1);
const pageSize = ref(10);
const curAnswerKlg = ref<klgType[]>([]);
const route = useRoute();

// 计算未删除的任务数量
const tasksLength = computed(() => {
  return ruleForm.list.filter((task) => !task.deleted).length;
});

const curQuestion = reactive<questionItem>({
  questionId: -1,
  questionDescription: '',
  associatedWords: '',
  keyword: '',
  questionType: '是什么',
  questionNecessity: qMode.ness,
  questionWeight: qWeight.open,
  userName: userinfo.getUsername(),
  createTime: '',
  answerNumber: 0,
  questionState: '',
  canDelete: true
});
const curAnswer = reactive<answerItem>({
  answerExplanation: '',
  answerId: -1,
  answerStatus: -1,
  createTime: '',
  keyword: '',
  klgNumber: 0,
  title: '',
  type: 0,
  questionType: '',
  taskNumber: 0,
  questionDescription: ''
});
const curAnswerDetail = reactive<questionDetail>({
  questioner: '',
  answerer: '',
  answerExplanation: '',
  answerKlgs: [],
  createTime: '',
  answerStatusList: []
});

const ruleForm = reactive({
  answer: '',
  list: []
});
const rule4answer = reactive<FormRules>({
  answer: [{ required: true, message: '请输入答案', trigger: 'blur' }],
  list: []
});

// 处理打开query中questionId的详情
const handleQueryQuestion = () => {
  const openQId = routerPushStore.getToOpenQuesId();
  if (openQId) {
    curQuestion.questionId = parseInt(openQId);
    showDrawer({ mode: 3, item: openQId });
    routerPushStore.setToOpenQuesId(null);
  }
};

// 展示抽屉
const showDrawer = (query: any) => {
  isExpanded.value = false;
  const { mode, item } = query;
  curMode.value = mode; // 2:不可操作
  switch (curMode.value) {
    case 2:
      drawerTitle.value = '回答问题';
      Object.assign(curQuestion, item);
      break;
    case 3:
      drawerTitle.value = '查看问题';
      const toShowQues = reactive<questionItem>({
        questionId: item ? item : curQuestion.questionId,
        questionDescription: '',
        associatedWords: '',
        keyword: '',
        questionType: findKeyByValue(qType.what, qTypeDict) as string,
        questionNecessity: qMode.ness,
        questionWeight: qWeight.open,
        userName: userinfo.getUsername(),
        createTime: '',
        answerNumber: 0,
        questionState: '',
        canDelete: false
      });
      Object.assign(curQuestion, toShowQues);
      getAnswers();
      break;
    default:
      console.log('问题弹窗mode异常');
      break;
  }
  isDrawerShow.value = true;
};

const topAnswer = ref();
const filterAnswerList = ref([]);

// 获取答案列表
const getAnswers = () => {
  getAnswersApi(curQuestion.questionId.toString()).then(async (res) => {
    if (res.success) {
      Object.assign(curQuestion, res.data.questionAndAnswer[0]);
      answerList.value = res.data.questionAndAnswer[0].answers;
      filterAnswerList.value = answerList.value.filter((item) => item.releaseStatus === 1);
      console.log(filterAnswerList.value);
      topAnswer.value = answerList.value.find((item) => item.pinToTop === 1) || null;
    }
  });
};

// 处理添加任务
const handleAddTask = () => {
  if (qTypeDict[curQuestion.questionType] === qType.what && tasksLength.value === 1) {
    ElMessage.info('最多只能添加一条');
    return;
  }
  const task = ref<taskTypePlus>({
    klgName: '',
    areaTitle: '',
    areaCode: '',
    taskStatus: 0,
    handlerName: '',
    feedback: null,
    oid: null,
    taskStyle: '',
    deleted: false
  });
  ruleForm.list.push(task.value);
};

// 处理删除任务
const handleDelTask = (task: taskType) => {
  // 使用findIndex找到task在数组中的索引
  const index = ruleForm.list.findIndex(
    (t) => t.areaTitle === task.areaTitle && t.klgName === task.klgName
  );
  // 如果找到了任务，就使用splice方法删除它
  if (index !== -1) {
    ruleForm.list.splice(index, 1);
  }
};
// 处理关闭抽屉
const handleClose = () => {
  window.getSelection()?.empty();
  curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
  ruleForm.list.splice(0, ruleForm.list.length);
  answerList.value.splice(0, answerList.value.length);
  ruleForm.answer = '';
  isDrawerShow.value = false;
};
// 处理点击回答去添加问题
const changeCurMode = () => {
  handleClose();
  dialogStore.question = curQuestion;
  dialogStore.answerDialogVisible = true;
  //showDrawer({ mode: mode, item: null });
};

const openDetailDialog = () => {
  handleClose();
  dialogStore.question = curQuestion;
  dialogStore.questionDialogVisible = true;
};

// 生成klg字符串
const generateKlgString = () => {
  return curAnswerKlg.value.map((item) => item.code).join('@@');
};
// 处理回答问题
const handleAnsQuesSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const toAddTaskList = ref<taskType[]>([]);
      toAddTaskList.value = ruleForm.list.map((task) => {
        return {
          klgName: task.klgName,
          areaTitle: task.areaTitle,
          areaCode: task.areaCode
        };
      });
      const params = ref<params2AddAns>({
        questionId: curQuestion.questionId,
        answerKlgs: generateKlgString(),
        answerExplanation: ruleForm.answer,
        taskList: toAddTaskList.value
      });
      addAnswerApi(params.value).then((res) => {
        if (res.success) {
          ElMessage.success('添加成功');
          emitter.emit(Event.REFRESH_QUESTION_LIST, true);
          handleClose();
        } else {
          ElMessage.warning(res.message);
        }
      });
    }
  });
};
// 筛选选项
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    const params = ref<params2tList>({
      limit: pageSize.value,
      current: currentPage.value,
      keyword: query,
      status: 1
    });
    getTagListApi(params.value).then((res) => {
      // @ts-ignore
      if (res.success) {
        loading.value = false;
        tagList.value = res.data.list;
      }
    });
  } else {
    tagList.value = [];
  }
};
// 筛选klg选项
const remoteKlgMethod = (query: string) => {
  if (query) {
    loading.value = true;
    getKlgListApi(query, qTypeDict[curQuestion.questionType]).then((res) => {
      if (res.success) {
        loading.value = false;
        kList.value = res.data;
      }
    });
  } else {
    kList.value = [];
  }
};
// 处理选择klg
const handleSelectKlg = (item: klgType) => {
  if (qTypeDict[curQuestion.questionType] === qType.what) {
    if (curAnswerKlg.value.length === 1) {
      ElMessage.error('最多选择1个标签');
      return;
    }
  }
  item.choose = !item.choose;
  const index = curAnswerKlg.value.findIndex((i: klgType) => i.code === item.code);
  if (index === -1) {
    curAnswerKlg.value.push(item);
  } else {
    curAnswerKlg.value.splice(index, 1);
  }
};

// 处理选择tag
const handleSelectTag = (tag: tagType, item: taskTypePlus) => {
  item.areaCode = tag.areaCode;
  item.areaTitle = tag.title;
};

const handleTopAnswer = (ansId: number) => {
  ElMessageBox.confirm('确定置顶这条回答吗？', '置顶回答', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    const params = ref<params2TopAns>({
      answerId: ansId
    });
    TopAnswerApi(params.value).then((res) => {
      if (res.success) {
        getAnswers();
        ElMessage.success('置顶成功！');
        emitter.emit(Event.REFRESH_QUESTION_LIST, true);
      }
    });
  });
};

// 处理删除答案--这里不删答案了
const handleDeleteAnswer = (ansId: number) => {
  ElMessageBox.confirm('确定删除这条回答吗？', '删除问题', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = ref<params2DelAns>({
        answerId: ansId
      });
      deleteAnswerApi(params.value).then((res) => {
        if (res.success) {
          getAnswers();
          ElMessage.success('删除成功！');
          emitter.emit(Event.REFRESH_QUESTION_LIST, true);
        }
      });
    })
    .catch();
};

// 处理删除问题
const handleDeleteQues = () => {
  if (curQuestion.canDelete) {
    ElMessageBox.confirm('确定删除问题吗？', '删除问题', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        const params = ref<params2DelQues>({
          questionId: curQuestion.questionId
        });
        deleteQuestionApi(params.value).then((res) => {
          if (res.success) {
            handleClose();
            emitter.emit(Event.REMOVE_QUESTION, curQuestion.questionId);
            emitter.emit(Event.REFRESH_QUESTION_LIST, true);
            ElMessage.success('删除成功');
          } else {
            ElMessage.error(res.message);
          }
        });
      })
      .catch();
  }
};

// 处理删除tag
const handleDelete = (aimId: string) => {
  const newKlg: klgType[] = curAnswerKlg.value.filter((item) => item.code != aimId);
  curAnswerKlg.value = newKlg;
};
// 处理打开ToOpenQuestionId
const handleToOpenQues = () => {
  const id = routerPushStore.getToOpenQuesId();
  if (id) {
    showDrawer({ mode: 3, item: id });
    routerPushStore.setToOpenQuesId(null);
  }
};

// 添加展开状态管理
const isExpanded = ref(false);

// 切换展开/收起状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 判断是否需要显示展开按钮
const shouldShowExpandBtn = computed(() => {
  if (!topAnswer.value || !topAnswer.value.answerExplanation) return false;
  return topAnswer.value.answerExplanation.length > 200;
});

watch(
  () => tasksLength.value,
  (newValue) => {
    if (qTypeDict[curQuestion.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTagShow.value = false;
      } else if (newValue === 0) {
        isAddTagShow.value = true;
      }
    }
  }
);
watch(
  () => curAnswerKlg.value.length,
  (newValue) => {
    if (qTypeDict[curQuestion.questionType] === qType.what) {
      if (newValue === 1) {
        isAddTaskShow.value = false;
      } else if (newValue === 0) {
        isAddTaskShow.value = true;
      }
    }
  }
);
watch(
  () => route.query.uniqueCode,
  () => {
    if (route.query.uniqueCode) {
      handleQueryQuestion();
      router.push({
        query: {
          uniqueCode: route.query.uniqueCode
        }
      });
    }
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  handleQueryQuestion();
  emitter.on(Event.SHOW_ANSWER_DRAWER, showDrawer);
});
onUnmounted(() => {
  emitter.off(Event.SHOW_ANSWER_DRAWER, showDrawer);
});
defineExpose({
  showDrawer,
  isDrawerShow
});
</script>

<template>
  <el-drawer
    direction="ltr"
    class="question-drawer"
    size="600"
    v-model="isDrawerShow"
    :show-close="false"
    :close-on-click-modal="false"
    :z-index="10"
    style="color: var(--color-black)"
  >
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        {{ drawerTitle }}
      </div>
      <button class="el-drawer__close-btn btn-position" type="button" @click="handleClose">
        <i class="el-icon el-drawer__close"
          ><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
            <path
              fill="currentColor"
              d="M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
            ></path></svg
        ></i>
      </button>
    </template>
    <div class="line-title"></div>
    <!-- 回答问题表单  不再使用 -->
    <template v-if="curMode === 2">
      <el-form ref="ruleFormRef" :rules="rule4answer" :model="ruleForm">
        <div class="content-container">
          <span class="tip">
            <span class="ques-username" v-if="curMode === 2">
              <b>{{ curQuestion.userName }}</b>
            </span>
            <span class="ques-username" v-else-if="curMode === 3">
              <b>{{ curAnswerDetail.questioner }}</b>
            </span>
            的提问</span
          >
          <div class="related-content-container" style="background-color: white">
            <span style="max-width: 88%; display: flex">
              <b class="ques-mark">【</b
              ><b
                class="questionList markdown-content"
                style="overflow: hidden"
                v-html="renderMarkdown(curQuestion.keyword)"
              ></b
              ><b class="ques-mark">】</b>
            </span>
            <span class="q-type">{{ curQuestion.questionType }}</span>
          </div>
          <div v-if="qTypeDict[curQuestion.questionType] === qType.open">
            <span class="ques-decription">
              <span class="ck-content" v-html="curQuestion.questionDescription"></span>
            </span>
          </div>
          <el-form-item>
            <div class="ques-timebar">
              <span>
                {{ curQuestion.createTime }}
              </span>
            </div>
          </el-form-item>
          <!-- 是什么：1个知识点 开放问题：0个知识点 其他：无所谓 -->
          <el-form-item>
            <div class="line"></div>
          </el-form-item>
          <el-form-item>
            <span class="add-answer-user-info">
              <span
                ><b>{{ userinfo.getUsername() }} </b></span
              >
              <span style="margin-left: 10px">的回答</span>
            </span>
          </el-form-item>
          <el-form-item prop="answer">
            <InlineEditor
              v-model="ruleForm.answer"
              style="min-width: 100%; min-height: 150px; max-height: 580px"
              :showToolbar="true"
            ></InlineEditor>
          </el-form-item>
          <el-form-item v-if="qTypeDict[curQuestion.questionType] !== qType.open && isAddTagShow">
            <div style="width: 100%">
              <span>请把答案中的知识点选出来:</span>
              <div style="margin-top: 10px">
                <el-select
                  v-if="qTypeDict[curAnswer.questionType] !== qType.what"
                  multiple
                  filterable
                  suffix-icon="Search"
                  :fit-input-width="true"
                  remote
                  reserve-keyword
                  placeholder="请输入知识名称"
                  placement="top"
                  :remote-method="remoteKlgMethod"
                  :loading="loading"
                  collapse-tags
                  :remote-show-suffix="true"
                  :max-collapse-tags="0"
                >
                  <template #suffix-icon>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                  <el-option
                    v-for="item in kList"
                    :key="item.code"
                    :value="item.title"
                    :class="item.choose ? 'highlight' : ''"
                    style="width: 100%"
                    @click="handleSelectKlg(item)"
                  >
                    <!-- 该实现出现了bug，导致后边的v-html不渲染 -->
                    <div style="display: inline">
                      <span class="option-tag" :class="`option-tag${item.type}`"
                        >{{ item.type === 2 ? '领域' : '知识' }}
                      </span>
                    </div>
                    <div style="display: inline">
                      <span class="ck-content" v-html="item.title" id="p_inline"></span>
                    </div>
                  </el-option>
                </el-select>
                <el-select
                  v-else
                  filterable
                  :fit-input-width="true"
                  suffix-icon="Search"
                  remote
                  reserve-keyword
                  placeholder="请输入知识名称"
                  placement="top"
                  :remote-method="remoteKlgMethod"
                  :remote-show-suffix="true"
                  :loading="loading"
                >
                  <template #suffix-icon>
                    <el-icon>
                      <Search />
                    </el-icon>
                  </template>
                  <el-option
                    v-for="item in kList"
                    :key="item.code"
                    :label="item.title"
                    :value="item.title"
                    :class="item.choose ? 'highlight' : ''"
                    style="width: 100%"
                    @click="handleSelectKlg(item)"
                  >
                    <span class="option-tag" :class="`option-tag${item.type}`">{{
                      item.type === 2 ? '领域' : '知识'
                    }}</span>
                    <span class="ck-content" v-html="item.title"></span>
                  </el-option>
                </el-select>
              </div>
              <div class="selected-list" id="bottom">
                <my-tag
                  class="klg"
                  @delete="handleDelete"
                  :tag-id="item.code"
                  v-for="item in curAnswerKlg"
                  :key="item.code"
                >
                  <el-tooltip raw-content popper-class="tooltip-width">
                    <template #content>
                      <div class="tooltipHtml ck-content" v-html="item.title"></div>
                    </template>
                    <span class="htmlContent2 ck-content" v-html="item.title"></span>
                  </el-tooltip>
                </my-tag>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="
              isAddTagShow && isAddTaskShow && qTypeDict[curQuestion.questionType] !== qType.open
            "
          >
            <div class="line"></div>
          </el-form-item>
          <el-form-item v-if="qTypeDict[curQuestion.questionType] !== qType.open && isAddTaskShow">
            <div style="width: 100%">
              <div class="add-klg" style="width: 100%">
                <span> 如果没有找到合适的知识点,请点这里 </span>
                <span class="icon" @click="handleAddTask">
                  <img style="margin-top: 4px" src="@/assets/images/answerlist/u736.svg" />
                </span>
              </div>
            </div>
          </el-form-item>
          <el-form-item
            v-if="qTypeDict[curQuestion.questionType] !== qType.open && isAddTaskShow"
            v-for="(item, index) in ruleForm.list"
            :key="item.areaCode"
          >
            <div v-if="!item.deleted" class="task-list">
              <el-col :span="16">
                <el-form-item
                  :prop="`list.${index}.klgName`"
                  :rules="{
                    required: true,
                    message: '请输入任务名称',
                    trigger: 'blur'
                  }"
                  style="width: 100%"
                >
                  <!-- 编辑模式：显示输入框 -->
                  <el-input
                    v-if="!item.showPreview"
                    v-model="item.klgName"
                    style="width: 100%; margin-right: 10px"
                    placeholder="请输入任务名称，支持数学公式"
                    maxlength="128"
                    show-word-limit
                    @blur="item.showPreview = true"
                    @focus="item.showPreview = false"
                  />

                  <!-- 预览模式：显示渲染结果 -->
                  <div
                    v-else-if="item.klgName && item.showPreview"
                    class="preview-container"
                    style="width: 100%; margin-right: 10px"
                    @click="item.showPreview = false"
                  >
                    <div
                      class="preview flex-center"
                      v-html="convertMathFormulas(item.klgName)"
                    ></div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item
                  :prop="`list.${index}.areaTitle`"
                  :rules="{
                    required: true,
                    message: '请选择领域',
                    trigger: 'blur'
                  }"
                  style="width: 100%"
                >
                  <el-select
                    v-model="item.areaCode"
                    filterable
                    :fit-input-width="true"
                    suffix-icon="Search"
                    clearable
                    remote
                    reserve-keyword
                    placeholder="选择领域"
                    :remote-method="remoteMethod"
                    :remote-show-suffix="true"
                    :loading="loading"
                    :validate-event="true"
                    style="width: 100%"
                  >
                    <template #suffix-icon>
                      <el-icon>
                        <Search />
                      </el-icon>
                    </template>
                    <el-option
                      v-for="tag in tagList"
                      :key="tag.title"
                      :label="tag.title"
                      :value="tag.areaCode"
                      v-html="tag.title"
                      class="ck-content"
                      :class="item.areaCode === tag.areaCode ? 'highlight' : ''"
                      style="width: 100%"
                      @click="handleSelectTag(tag, item)"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <span class="icon" @click="handleDelTask(item)">
                <img src="@/assets/images/answerlist/u742.svg" />
              </span>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </template>

    <!-- 查看问题表单 -->
    <template v-else-if="curMode == 3">
      <el-form ref="ruleFormRef">
        <div class="content-container">
          <span class="tip">
            <span class="ques-username">
              <b>{{ curQuestion.userName }}</b>
            </span>
            的提问</span
          >
          <div class="keyword-container">
            <template v-if="curQuestion.questionType != '开放性问题'">
              <span
                class="keyWords textfont"
                style="font-weight: 600"
                v-html="'【' + curQuestion.keyword + '】'"
              ></span>
              <span>{{ curQuestion.questionType + '?' }}</span>
            </template>
            <template v-else>
              <div class="keyWords textfont" v-html="'【' + curQuestion.keyword + '】'"></div>
            </template>
          </div>

          <el-form-item>
            <div style="display: flex; width: 100%">
              <div class="ques-timebar">
                <span>
                  {{ curQuestion.createTime }}
                </span>
              </div>
              <div class="to-answer-btn">
                <span class="btn1" @click="changeCurMode"> 回答 </span>
                <span class="btn2" v-if="curQuestion.canDelete" @click="handleDeleteQues">
                  删除
                </span>
              </div>
            </div>
          </el-form-item>
          <!--<el-form-item> </el-form-item>-->
          <!--待改-->
          <el-form-item>
            <div style="width: 100%" v-if="topAnswer && topAnswer.releaseStatus == 1">
              <div class="answer-item">
                <div class="top">置顶</div>
                <div class="info">
                  <span>
                    <span class="answer-name"
                      ><b>{{ topAnswer.userName }}</b></span
                    >
                    <span class="author" v-if="topAnswer.isAuthor === 1"> 作者 </span>
                    <span style="margin-left: 5px">的回答</span>
                  </span>
                  <span v-if="topAnswer.releaseStatus === 0" class="release-status"> 未发布 </span>
                </div>
                <div class="klg-list">
                  <my-tag
                    class="k"
                    :tag-id="klg.code"
                    v-for="klg in topAnswer.knowledgeList"
                    :key="klg.code"
                    :type="'klgTag'"
                    :deletable="false"
                  >
                    <el-tooltip raw-content popper-class="tooltip-width">
                      <template #content>
                        <div class="tooltipHtml ck-content" v-html="klg.title"></div>
                      </template>
                      <span class="htmlContent2 ck-content" v-html="klg.title"></span>
                    </el-tooltip>
                  </my-tag>
                </div>
                <div
                  class="answer-exp answer-exp-es"
                  :class="{ collapsed: !isExpanded, expanded: isExpanded }"
                >
                  <span class="markdown-content" v-html="topAnswer.answerExplanation"></span>
                </div>
                <div class="tooltip">
                  <span> {{ topAnswer.createTime }}</span>
                  <span
                    @click="toggleExpand"
                    v-if="shouldShowExpandBtn && !isExpanded"
                    style="color: var(--color-primary); cursor: pointer; font-size: 13px"
                    >展开<el-icon>
                      <CaretBottom /> </el-icon
                  ></span>
                  <span
                    @click="toggleExpand"
                    v-if="shouldShowExpandBtn && isExpanded"
                    style="color: var(--color-primary); cursor: pointer; font-size: 13px"
                    >收起<el-icon>
                      <CaretTop /> </el-icon
                  ></span>
                </div>
              </div>
            </div>
            <!--<div v-if="answerList.length > 0 && !topAnswer" style="border-left: 1px solid #dcdfe6; width: 100%;">
              <span>暂无置顶回答</span>
            </div>-->
            <div v-if="filterAnswerList.length > 0" class="total-answer">
              <span style="color: var(--color-primary); cursor: pointer" @click="openDetailDialog"
                >共{{ filterAnswerList.length }}条回答</span
              >
              <el-icon style="color: var(--color-primary)">
                <CaretBottom />
              </el-icon>
            </div>
            <!--<div v-else>
              <span>暂无回答</span>
            </div>-->
          </el-form-item>
        </div>
      </el-form>
    </template>
    <!--
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <template v-if="curMode === 3"> </template>
          <template v-else-if="curMode === 2">
            <my-button type="light" @click="changeCurMode(3)">返回</my-button>
            <my-button @click="handleAnsQuesSubmit">提交</my-button>
          </template>
        </div>
      </div>
    </template>-->
  </el-drawer>
</template>

<style scoped lang="less">
:deep(li::marker) {
  content: "";
  color: transparent;
}

@import 'katex/dist/katex.min.css';

#p_inline > p {
  display: inline;
}

.el-drawer__header {
  position: relative;
  padding-bottom: 10px;
}

.el-drawer__header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}

:deep(.el-form-item__content) {
  align-items: center;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  font-size: var(--font-size);
  line-height: 19px;
  min-width: 0;
  position: relative;
}

:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}

.answer-exp-es {
  /* 关键样式：限制三行省略 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  /* 显示3行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  line-height: 1.5;
  /* 每行高度（可根据需要调整） */
  max-height: calc(1.5em * 3);
  /* 三行总高度（与line-height对应） */
}

.answer-exp-es.collapsed {
  -webkit-line-clamp: 3;
  max-height: calc(1.5em * 3);
}

.answer-exp-es.expanded {
  -webkit-line-clamp: unset;
  max-height: none;
}

.highlight {
  background-color: var(--color-light);
  color: var(--color-primary);
}

.question-drawer {
  color: var(--color-black);
}

.icon {
  width: 30px;
  margin-left: 10px;

  &:hover {
    cursor: pointer;
  }
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;

  .klg {
    margin-bottom: 5px;
    margin-right: 10px;
    border-radius: 4px;
  }
}

.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);

  &:hover * {
    font-weight: 600;
  }

  &.selected {
    color: var(--color-primary);
  }
}

.title {
  font-size: 16px;
  font-family: var(--font-family-text);
  font-weight: 700;
  color: #333333;
  padding-bottom: 10px;
}

.btn-position {
  position: absolute;
  top: 10px;
  right: 0;
}

.line-title {
  width: 580px;
  height: 1px;
  background-color: #ccc;
  position: absolute;
  top: 50px;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #ccc;
}

.option-tag {
  margin-right: 10px;
  border: 1px solid black;
  padding: 0 5px;
  border-radius: 3px;
  font-weight: 400 !important;
}

.option-tag1 {
  color: #606266;
}

.option-tag2 {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.content-container {
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 0 20px;

  :deep(.is-disabled .el-textarea__inner),
  :deep(.is-disabled .el-input__wrapper) {
    background-color: white;
    resize: none;
    /*禁止文本域拖拽事件*/
  }

  .input {
    /* width: 800px; */
    width: 100%;
    max-width: 800px;
    line-height: 20px;
    display: inline-block;
  }

  .q-type {
    display: inline-block;
    font-weight: bold;
    padding-left: 5px;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 400;
  }

  .el-select {
    width: 100%;
  }

  .not-select-tag {
    background-color: white;
    color: black;
    border: 1px solid var(--color-primary);
  }

  .select-tag {
    &:hover {
      background-color: var(--color-primary);
      color: white;
    }
  }

  .ques-decription {
    display: flex;
    text-align: center;
    min-width: 100%;
    //border: 1px solid var(--color-primary);
    border-radius: 5px;
    padding: 10px;
  }

  .ques-username {
    margin-right: 10px;
    font-size: 13px;
    font-weight: 400;
  }

  .ques-mark {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
  }

  .ques-timebar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--color-grey);
    font-size: 12px;
    font-weight: 400;

    .answer {
      font-style: normal;
      font-size: 13px;
      color: #1973cb;
      text-align: right;
      font-weight: 400;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .to-answer-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn1 {
      margin-right: 15px;
      color: var(--color-primary);

      &:hover {
        cursor: pointer;
        font-weight: bold;
      }
    }

    .btn2 {
      &:hover {
        cursor: pointer;
        font-weight: bold;
      }
    }
  }

  .answer-item {
    width: 100%;
    padding: 0 10px;
    margin-left: 5px;
    //background-color: #f2f2f2;
    border-left: 1px solid #dcdfe6;
    //margin-bottom: 25px;
    border-radius: 3px;

    .top {
      width: 40px;
      height: 20px;
      font-size: 12px;
      font-weight: 400;
      text-align: center;
      color: white;
      background-color: var(--color-primary);
      padding: 2px;
      margin-bottom: 5px;
    }

    .info {
      margin-bottom: 10px;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      width: 80%;

      .answer-name {
        font-size: 12px;
        font-weight: 600;
      }

      .author {
        margin-left: 5px;
        background-color: rgba(238, 162, 60, 0.1);
        border: 1px solid rgba(238, 162, 60, 0.3);
        padding: 3px 5px;
        color: #e6a23c;
        border-radius: 4px;
        height: 8px;
      }

      .release-status {
        padding: 0 5px;
        border: 1px solid;
        border-color: var(--color-release-status);
        border-radius: 3px;
        color: var(--color-release-status);
        background-color: var(--color-release-status-rgba);
      }
    }
  }

  .answer-item2 {
    width: 100%;
    padding: 10px 10px;
    margin-bottom: 10px;
    border-radius: 3px;
    border-bottom: 1px solid #dcdfe6;

    .info {
      margin-bottom: 10px;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      width: 80%;

      .author {
        margin-left: 5px;
        background-color: rgba(238, 162, 60, 0.1);
        border: 1px solid rgba(238, 162, 60, 0.3);
        color: #e6a23c;
        border-radius: 4px;
        height: 8px;
      }

      .release-status {
        padding: 0 5px;
        border: 1px solid;
        border-color: var(--color-release-status);
        border-radius: 3px;
        color: var(--color-release-status);
        background-color: var(--color-release-status-rgba);
      }
    }
  }

  .total-answer {
    margin-left: 5px;
    padding-left: 10px;
    font-size: 13px;
    border-left: 1px solid #dcdfe6;
  }

  .klg-list {
    display: flex;
    max-width: 100%;
    flex-wrap: wrap;

    .k {
      width: 120px;
      height: 25px;
      padding: 4px 20px;
      font-size: 12px;
      margin-right: 10px;
      border-radius: 4px;
      margin-bottom: 5px;
    }
  }

  .answer-exp {
    margin-bottom: 20px;
    font-size: 16px;
    overflow: hidden;
  }

  .tooltip {
    display: flex;
    justify-content: space-between;
    color: var(--color-deep);
    font-size: 12px;

    .delete-text:hover {
      color: var(--color-primary);
    }

    .top-text:hover {
      font-weight: bold;
    }
  }

  .tip {
    //margin-bottom: 6px;
    font-size: 12px;
    font-weight: 400;
  }

  .related-content-container {
    display: flex;
    align-items: center;
    padding: 15px 5px;
    background-color: var(--color-light);
    word-break: break-all;
  }

  .type-container {
    margin-top: 10px;
    display: flex;
    width: 100%;
    justify-content: space-between;
    white-space: nowrap;
    align-items: center;

    .type-tip {
      font-size: 14px;
      font-weight: 600;
    }
  }

  .klg-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }

  .add-klg {
    width: 100%;
    display: flex;
    align-items: center;
  }

  .task-list {
    width: 100%;
    display: flex;
    margin-bottom: 5px;
    justify-content: space-between;

    .select-area {
      margin-left: 10px;
      width: 35%;
    }

    .icon {
      margin-left: 10px;
      display: flex;
      align-items: center;

      &:hover {
        cursor: pointer;
      }
    }
  }

  .add-answer {
    width: 100%;

    .selectedTList {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      overflow: auto;

      .t {
        margin: 0 10px 10px 0;
        padding: 0 10px 0 10px;
      }
    }

    .answer-tooltip {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .answer-submit {
        padding: 5px;
      }
    }
  }

  .button-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}

.el-drawer {
  :deep(.el-drawer__header) {
    margin-bottom: 0px;
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }

  .pagination {
    display: flex;
    flex-direction: row;

    .btn {
      cursor: pointer;
      margin: 0 5px;
      display: flex;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: var(--color-grey);

      &.focused,
      &:hover {
        background-color: var(--color-primary);
      }
    }
  }
}
.keyword-container {
    margin-bottom: 20px;
    .keyword {
      font-weight: 700;
      word-break: break-all;
      margin-left: 0 !important;
    }
  }

.preview-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 12px;
    background-color: #ffffff;
    cursor: pointer;
    height: 32px;
    display: flex;
    align-items: center;
    
    .preview {
        line-height: 1.4;
        color: #606266;
        width: 100%;
    }
    
    &:hover {
        border-color: #c0c4cc;
    }
}
</style>
