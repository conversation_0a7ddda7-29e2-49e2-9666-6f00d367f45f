import { APIResponse, taskType } from "../../utils/type";
import { http } from "../index";
export interface taskItem {
  oid: number | null;
  klgName: string;
  areaTitle: string;
  areaCode: string;
  deleted: boolean;
}

export interface params2getAnsList {
  type: number,
  current: number;
  limit: number;
  answerStatus: number;
  answerExplanation: string;
  keyword?: string;
}
export interface params2AddAns {
  questionId: number;
  answerKlgs: string;
  answerExplanation: string;
  taskList: taskType[];
}

export interface params2EditAns {
  answerId: number;
  answerKlgs: string;
  answerExplanation: string;
  taskList: taskItem[];
}

export interface params2DelAns {
  answerId: number;
}

export interface params2TopAns {
  answerId: number;
}

// 获取答案列表
export function getAnswerListApi(
  params: params2getAnsList
): Promise<APIResponse> {
  return http.post("/answer/getAnswerList", params);
}
// 获取答案列表new with exercise
export function getAnswerPageListApi(
  params: params2getAnsList
): Promise<APIResponse> {
  return http.post("/exercise/answer/query", params);
}


// 置顶anserID编号的回答
export function TopAnswerApi(params: params2TopAns): Promise<APIResponse> {
  return http.post("/answer/pinToTop", params);
}

// 删除答案
export function deleteAnswerApi(params: params2DelAns): Promise<APIResponse> {
  return http.post("/answer/deleteAnswer", params);
}

// 增加答案
export function addAnswerApi(params: params2AddAns): Promise<APIResponse> {
  return http.post(`/answer/saveAnswer`, params);
}
// 发布答案
export function publishAnswerApi(answerId: number): Promise<APIResponse> {
  return http.get(`/answer/releaseAnswer?answerId=${answerId}`);
}
// 获得答案详情
export function getAnswerDetailApi(answerId: number): Promise<APIResponse> {
  return http.get(
    `/answer/getAnswerDetail?answerId=${answerId}`
  );
}
// 编辑答案详情
export function editAnswerApi(params: params2EditAns): Promise<APIResponse> {
  return http.post(`/answer/editAnswer`, params);
}
