<script setup lang="ts">
const props = defineProps({
  stepMode: {
    type: Number,
    required: false,
    default: 2,
  },

});

</script>

<template>
  <div class="exam-container">
    <div class="index">
      <slot name="index"></slot>
    </div>
    <div class="type">
      <slot name="type"></slot>
    </div>
    <div class="content">
      <slot name="content"></slot>
    </div>
    <div class="footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<style scoped>
.exam-container {
  width: 100%;
  height: 46px;
  display: flex;
  align-items: center;
  font-size: 14px;
  *{
    display: flex;
    height: 100%;
    align-items: center;
  }
  .index {
    width: 62px;
    justify-content: center;
  }
  .type {
    width: 124px;
    justify-content: center;
  }
  .content {
    width: 932px;
    padding: 0 10px;
  }
  .footer {
    width: 22px;
  }
}
</style>