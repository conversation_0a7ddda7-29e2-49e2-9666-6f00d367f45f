// 主题样式
$color__primary: #1661ab;
$color__secondary-hover: #c5d5ea;
$color__secondary: #9eb7e5;
$color__warning: #ea1e1e;
$color--white: white;

$color__black: #000;
$color__font-primary--dark: #333333;
$color__font-secondary-header--rich: #666666;
$color__font-secondary--medium: #999999;
$color__font-tip--soft: #c0c4cc;
$color__shadow--soft: #cccccc;
$color__font-invalid--light: #f0f0f0;

$color__sketch-valid: #036558;
$color__invalid: #9d1111;
$color__success: #67c23a;

$background-color--primary: #ffffff;
$background-color--base: #fafafa;
$background-color__flipper: #dcdfe6;

$color__shadow-spare--light: #d8e1e9;
$color__shadow-spare--dark: #b3c5d7;

// 导航栏
$height__header: 55px;
$margin-bottom__header: 5px;
$font-size__logo-top: 24px;
$font-size__logo-bottom: 10px;
$font-size__logo-title: 20px;

// 侧边栏
$width__aside: 200px;
$height__aside: calc(100vh - 65px);
$height__aside-item: 60px;
$font-size__aide-item: 16px;
$font-size__aside-copyright: 14px;
$background-color__aside-item-hover: rgb(206, 206, 206);

// 字体族
$font-family-puhuiti-L3: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular',
    '阿里巴巴普惠体 3.0', sans-serif;
$font-family-puhuiti-semibold: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular',
    '阿里巴巴普惠体 3.0', sans-serif;

$font-family-puhuiti-55: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular',
    '阿里巴巴普惠体 3.0', sans-serif;

$font-family-fangyuanti-semibold: 'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular',
    'Alimama FangYuanTi VF', sans-serif;

$font-family-Agile-italic: 'Alimama Agile VF Italic', 'Alimama Agile VF', sans-serif;

// 字体大小
$font-size--huge: 26px;
$font-size--large: 20px;
$font-size--big: 18px;
$font-size--medium: 16px;
$font-size--small: 14px;
$font-size--tiny: 12px;

// 信息展示区域
$width__main: 1200px;
$width__main-short: 1000px;
$margin-top__main: 15px;

$height__info-line: 59px;
$width__info-line-label: 100px;

$height__info-header: 70px;

$padding-left__info-header-identity: 30px;
$padding-top-bottom__info-line: 20px;

$width-height__info-avatar: 64px;

// 组件样式

$border-radius__select: 2px;
$box-shadow__select-input: 0 0 0 1px $color__font-secondary--medium inset;

@mixin under_line {
    &::after {
        content: '';
        height: 2px;
        width: 100%;
        background-color: $color__font-invalid--light;
        position: absolute;
        bottom: 0;
    }
}
