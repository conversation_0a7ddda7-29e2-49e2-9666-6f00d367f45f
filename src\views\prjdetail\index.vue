<script setup lang="ts">
import PrjInfo from "@/components/PrjInfo.vue"
import MySteps from "./components/mySteps.vue"
import SectionSwitcher from "./components/sectionSwitcher.vue"
import TextQuestion from "./textQuestion.vue"
import VideoQuestion from "./videoQuestion.vue"

import {
  nextTick,
  onBeforeMount,
  onMounted,
  onUnmounted,
  provide,
  ref,
  watch,
} from "vue"
import { useRoute } from "vue-router"
import { getProjectDetailApi } from "@/apis/path/prjdetail"
import { formatData_step1 } from "@/utils/func"
import type { prjInfoType, simpleSectionInfo, tagType } from "@/utils/type"
import { prjForm, prjType, typeDict_noDefault } from "@/utils/constant"
import { ElMessage } from "element-plus"
import { useDraftWordStoreV2 } from "@/stores/draftWord"
import { useVideoWordStoreV2 } from "@/stores/videoWord"
import { getQuestionList } from "@/utils/lineWord2V2"
import { emitter } from "@/utils/emitter"
import { Event } from "@/types/event"
import { useRouterPushStore } from "@/stores/routerPushStore"

const draftWordStore = useDraftWordStoreV2()
const videoWordStore = useVideoWordStoreV2()
const route = useRoute()
const routerPushStore = useRouterPushStore()

const curPrjForm = ref<prjForm>(prjForm.video)
const curPrjType = ref<prjType>(prjType.default)
const uniqueCode = ref("") // uniqueCode：string
const curChapterId = ref(-1)
const curSecCount = ref() // 只为单节视频提供
const toOpenQuesId = ref()
const questionOnwer = ref({
  uniqueCode: "",
  onwerType: 1,
})
provide("toOpenQuestion", toOpenQuesId)
const prjInfoData = ref<prjInfoType>({
  prjType: prjType.exam,
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
})
const secList = ref<simpleSectionInfo[]>([])

// 获取项目详情
const getProjectDetail = () => {
  const baseData = routerPushStore.getData()
  curPrjForm.value = baseData.prjForm
  curPrjType.value = baseData.prjType
  prjInfoData.value = formatData_step1(baseData)
  if (baseData.sectionList) {
    secList.value = baseData.sectionList?.map((sec: any) => {
      return {
        sectionId: sec.sectionId,
        sectionName: sec.sectionTitle,
        sectionCount: sec.sectionNum,
      }
    })
    curChapterId.value = secList.value[0].sectionId
    curSecCount.value = secList.value[0].sectionNum
  }
}
const handleChangeSection = (newChapterId: number) => {
  curChapterId.value = newChapterId
  draftWordStore.regString = ""
  videoWordStore.regString = ""
}

const handleClickWord = (el: HTMLElement) => {
  // 如果元素为空或没有data-qid属性，直接返回
  if (!el) return
  const id = (el as HTMLElement).getAttribute("data-qid")
  if (!id) {
    console.log("元素没有data-qid属性")
    return
  }

  const idList = id.split(",")
  if (idList.length > 1) {
    getQuestionList(id).then((questionList) => {
      const query = {
        questionList: questionList,
        element: el,
      }
      nextTick(() => {
        emitter.emit(Event.SHOW_MUL_LIST, query)
      })
    })
  } else if (idList.length === 1) {
    emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: parseInt(id) })
  }
}

onMounted(() => {
  if (route.query.questionId) {
    toOpenQuesId.value = route.query.questionId
  }
})
onMounted(() => {
  const resData = routerPushStore.getData()
  console.log("resData", resData)
  emitter.on(Event.CLICK_WORD, handleClickWord)
})
onUnmounted(() => {
  emitter.off(Event.CLICK_WORD, handleClickWord)
})

watch(
  () => route.query.uniqueCode,
  (newVal) => {
    if (newVal) {
      uniqueCode.value = newVal as string
      questionOnwer.value.uniqueCode = uniqueCode.value
      getProjectDetail()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <div class="main-container">
    <my-steps :action="3" :form="parseInt(curPrjForm)"></my-steps>
    <div class="content-wrapper">
      <prj-info :info-data="prjInfoData"></prj-info>
      <template v-if="(curPrjForm == prjForm.video && curPrjType == prjType.prj) ||
        (curPrjForm == prjForm.text && curPrjType != prjType.klg)
        ">
        <section-switcher @changeSection="handleChangeSection" :curSectionId="curChapterId"
          :sectionList="secList"></section-switcher>
      </template>
      <template v-if="curPrjForm == prjForm.text &&
        typeDict_noDefault[prjInfoData.prjType] == prjType.exam
        ">
      </template>
      <template v-else-if="curPrjForm == prjForm.text">
        <!-- 文稿-> 案例|讲解 -->
        <text-question :cur-project-id="uniqueCode" :cur-section-id="curChapterId"></text-question>
      </template>
      <template v-else-if="curPrjForm == prjForm.video">
        <!-- 视频 -->
        <video-question :cur-project-id="uniqueCode" :cur-section-id="curChapterId"></video-question>
      </template>
    </div>
  </div>
  <!-- 其他组件 -->
  <!-- 问题抽屉 -->
  <question-drawer ref="questionDrawerRef" :ques-onwer="questionOnwer"></question-drawer>
  <answer-drawer ref="answerDrawerRef" :ques-onwer="questionOnwer"></answer-drawer>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: 1300px;
  margin: 20px auto auto auto;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 25px 30px;
  }

  .tool-bar {
    width: 420px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
