import { ElementType, parseDocument } from 'htmlparser2';
import { Document, Element, type ChildNode, type Text } from 'domhandler';
import { render } from 'dom-serializer';
import { type RenderInfo } from '@/types/word';
import URLParse from 'url-parse';
import { trim } from 'lodash-es';
import { decodeHTML } from 'entities';
import hljs from 'highlight.js';
import katex from 'katex';
import { getVisibleLatex } from '@/utils/getVisibleLatex';

onmessage = function (
  e: MessageEvent<{
    htmlString: string;
    regString?: string;
    renderInfoIndexes?: number[];
    renderInfoList?: RenderInfo[];
  }>
) {
  const start = Date.now();
  let { htmlString, regString, renderInfoIndexes, renderInfoList } = e.data;
  if (!regString) {
    const res = build(htmlString);
    regString = res.regString;
    renderInfoIndexes = res.renderInfoIndexes;
    renderInfoList = res.renderInfoList;
  }
  console.log('build', Date.now() - start);
  postMessage({
    regString,
    renderInfoIndexes,
    renderInfoList
  });
};

function build(htmlString: string) {
  const renderInfoIndexes: number[] = [];
  const dom = parseDocument(htmlString, {
    decodeEntities: false
  });

  let index = 0; //渲染字符串位置索引
  let regString = '';
  const renderInfoList: RenderInfo[] = [];
  const dfs = (node: ChildNode) => {
    if (node.type == ElementType.Text) {
      const textContent = node.data;
      for (let i = 0; i < textContent.length; ++i) {
        if (textContent[i] == '&') {
          const end = textContent.indexOf(';', i);
          if (end == -1) {
            throw new Error('转义字符异常');
          }
          const entity = textContent.slice(i, end + 1);

          renderInfoList.push({
            content: entity,
            isTag: false,
            relatedQuestionCount: 1,
            relatedSearchCount: 1,
            questionCountMap: {},
            searchCount: 0
          });
          regString += decodeHTML(entity);
          renderInfoIndexes.push(index);
          index++;

          i = end;
        } else {
          if (textContent[i] != '\r') {
            renderInfoList.push({
              content: textContent[i],
              isTag: false,
              relatedQuestionCount: 1,
              relatedSearchCount: 1,
              questionCountMap: {},
              searchCount: 0
            });
            regString += textContent[i];
            renderInfoIndexes.push(index);
            index++;
          }
        }
      }
    } else if (node.type == ElementType.Script) {
      const display = node.attribs.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;
      const code = (node.firstChild as Text).data;

      // console.log(key);
      const outerHTML = katex.renderToString(code, {
        throwOnError: false,
        output: 'html'
      });
      const str = display
        ? `<div class="equation" latexCode="${code}">${outerHTML}</div>`
        : `<span class="inline-equation" latexCode="${code}">${outerHTML}</span>`;
      const key = getVisibleLatex(outerHTML);
      renderInfoList.push({
        content: str,
        isTag: false,
        relatedQuestionCount: key.length,
        relatedSearchCount: 1,
        questionCountMap: {},
        searchCount: 0
      });
      regString += key;
      for (let i = 0; i < key.length; ++i) {
        renderInfoIndexes.push(index);
      }
      index++;
      // node.attribs.stringIndex = String(index - 1);
      // const display = node.attribs.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;
      // const outerHTML = renderToString(key, {
      //   displayMode: display,
      //   throwOnError: true,
      //   output: 'html'
      // })
      // console.log(outerHTML)
      // const outerHTML = render(node);
      // if (!uncommonWordMap.has(key)) {
      //   regString += uncommonWords[uncommonWordIndex];
      //   renderInfoList.push({
      //     content: outerHTML,
      //     qids: [],
      //     search: false,
      //     isTag: false
      //   });
      //   uncommonWordMap.set(key, uncommonWords[uncommonWordIndex++]);
      //   renderInfoIndexes.push(index++);
      // } else {
      //   const str = uncommonWordMap.get(key)!;
      //   regString += str;
      //   renderInfoList.push({
      //     content: outerHTML,
      //     qids: [],
      //     search: false,
      //     isTag: false
      //   });
      //   renderInfoIndexes.push(index++);
      // }
    } else if (node.type == ElementType.Tag) {
      if (node.tagName == 'img') {
        node.attribs.src = decodeHTML(node.attribs.src);
        const src = node.attribs.src;
        const outerHTML = render(node);
        const key = decodeURI(URLParse(src).pathname.split('/').pop() as string);

        renderInfoList.push({
          content: outerHTML,
          isTag: false,
          relatedQuestionCount: key.length,
          relatedSearchCount: Infinity,
          questionCountMap: {},
          searchCount: 0
        });
        regString += key;
        for (let i = 0; i < key.length; ++i) {
          renderInfoIndexes.push(index);
        }
        index++;
        // if (!uncommonWordMap.has(key)) {
        //   regString += uncommonWords[uncommonWordIndex];
        //   renderInfoList.push({
        //     content: outerHTML,
        //     qids: [],
        //     search: false,
        //     isTag: false
        //   });
        //   uncommonWordMap.set(key, uncommonWords[uncommonWordIndex++]);
        //   renderInfoIndexes.push(index++);
        // } else {
        //   const str = uncommonWordMap.get(key)!;
        //   regString += str;
        //   renderInfoList.push({
        //     content: outerHTML,
        //     qids: [],
        //     search: false,
        //     isTag: false
        //   });
        //   renderInfoIndexes.push(index++);
        // }
      }
      //1
      else if (node.tagName == 'code') {
        const code = (node.children[0] as Text).data;
        node.attribs.class = `${node.attribs.class} hljs`;

        const outerHTML = decodeHTML(hljs.highlightAuto(code).value);
        // console.log(outerHTML);
        let str = `<code`;
        for (const key in node.attribs) {
          str += ` ${key}="${node.attribs[key]}"`;
        }
        str += `>`;
        renderInfoList.push({
          content: str,
          isTag: true,
          relatedQuestionCount: Infinity,
          relatedSearchCount: Infinity,
          questionCountMap: {},
          searchCount: 0
        });
        index++;
        // const strArr = outerHTML.split('\n');

        // strArr.forEach((str) => {
        const dom = parseDocument(outerHTML, {
          decodeEntities: false
        });
        dfs(dom);
        // renderInfoList.push({
        //   content: '\n',
        //   qids: [],
        //   search: false,
        //   isTag: false
        // });
        // index++;
        // });
        renderInfoList.push({
          content: `</code>`,
          isTag: true,
          relatedQuestionCount: Infinity,
          relatedSearchCount: Infinity,
          questionCountMap: {},
          searchCount: 0
        });
        index++;
      }
      //2
      else {
        let content = `<${node.tagName}`;
        for (const key in node.attribs) {
          content += ` ${key}="${node.attribs[key]}"`;
        }
        content += '>';
        renderInfoList.push({
          content,
          isTag: true,
          relatedQuestionCount: Infinity,
          relatedSearchCount: Infinity,
          questionCountMap: {},
          searchCount: 0
        });
        index++;
        for (const child of node.children) {
          dfs(child);
        }
        if (!['br', 'hr'].includes(node.tagName)) {
          renderInfoList.push({
            content: `</${node.tagName}>`,
            isTag: true,
            relatedQuestionCount: Infinity,
            relatedSearchCount: Infinity,
            questionCountMap: {},
            searchCount: 0
          });
          index++;
        }
      }
    } else {
      for (const child of (node as Document).children) {
        dfs(child);
      }
    }
  };
  dfs(dom);
  return { renderInfoIndexes, regString, renderInfoList };
}
