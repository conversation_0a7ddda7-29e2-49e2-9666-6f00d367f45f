import { defineStore } from 'pinia';
import type { RenderInfo } from '@/types/word';
import { toRaw } from 'vue';

export const useExerciseWordStoreV2 = defineStore('exerciseWord', {
  state: () => ({
    regString: '',
    renderInfoListList: [] as RenderInfo[][],
    renderInfoIndexes: [] as Array<{
      listIndex: number;
      index: number;
    }>
  }),

  actions: {
    resetRenderInfoList(clearQid: boolean = true, clearSearch: boolean = true) {
      const renderInfoList = toRaw(this.renderInfoListList).flat();
      // const tempRenderList = this.renderInfoList.filter((item) => {
      //   return item.qids.length > 0 || item.search;
      // });
      // tempRenderList.forEach((item) => {
      //   if (clearQid) {
      //     item.qids = [];
      //   }
      //   if (clearSearch) {
      //     item.search = false;
      //   }
      // });
      renderInfoList.forEach((item) => {
        if (clearQid) {
          item.questionCountMap = {};
        }
        if (clearSearch) {
          item.searchCount = 0;
        }
      });
    },
    getAskModeVideoStrings(): string[] {
      const start = Date.now();
      const renderInfoListList = toRaw(this.renderInfoListList);
      const res = renderInfoListList.map((renderInfoList) => {
        return renderInfoList
          .map((item) => {
            return item.content;
          })
          .join('');
      });
      console.log('getAskModeVideoStrings', Date.now() - start);
      return res;
    },
    getReadModeVideoStrings(): string[] {
      const renderInfoListList = toRaw(this.renderInfoListList);
      const start = Date.now();
      const res = renderInfoListList.map((renderInfoList) => {
        let tempQid = null;
        let tempContent = null;
        let tempSearch = null;
        let unMatchTempText = '';
        let str = '';
        let index = 0;
        for (let i = 0; i < renderInfoList.length; ++i) {
          const renderInfo = renderInfoList[i];
          const matchQids = getMatchQids(renderInfo);
          if (matchQids.length || renderInfo.searchCount >= renderInfo.relatedQuestionCount) {
            if (unMatchTempText.length > 0) {
              str += unMatchTempText;
              // if (trim(unMatchTempText).length > 0) {
              index++;
              // }
              unMatchTempText = '';
            }
            if (tempQid == null) {
              tempQid = matchQids.join(',');
              tempContent = renderInfo.content;
              tempSearch = renderInfo.searchCount >= renderInfo.relatedQuestionCount;
            } else if (
              tempQid == matchQids.join(',') &&
              tempSearch == renderInfo.searchCount >= renderInfo.relatedQuestionCount
            ) {
              tempContent += renderInfo.content;
            } else {
              if (tempQid.length > 0) {
                str += `<span data-index="${index++}" class="highlight${tempSearch ? ' highlight2' : ''}" data-qid="${tempQid}">${tempContent}</span>`;
              } else {
                str += `<span class="highlight2">${tempContent}</span>`;
              }
              tempQid = matchQids.join(',');
              tempContent = renderInfo.content;
              tempSearch = renderInfo.searchCount >= renderInfo.relatedQuestionCount;
            }
          } else {
            if (tempQid != null) {
              str += `<span data-index="${index++}" class="highlight${tempSearch ? ' highlight2' : ''}" data-qid="${tempQid}">${tempContent}</span>`;
            }
            if (renderInfo.isTag) {
              if (unMatchTempText.length > 0) {
                str += unMatchTempText;
                // if (trim(unMatchTempText).length > 0) {
                index++;
                // }
                unMatchTempText = '';
              }
              str += renderInfo.content;
            } else {
              // 检查是否是公式元素
              if (renderInfo.content.includes('class="equation"') || renderInfo.content.includes('class="inline-equation"')) {
                // 为公式元素添加data-qid属性
                str += `<span data-index="${index++}" class="highlight" data-qid="">${renderInfo.content}</span>`;
              } else {
                unMatchTempText += renderInfo.content;
              }
            }
            tempQid = null;
            tempContent = null;
            tempSearch = null;
          }
        }
        if (tempQid != null) {
          str += `<span data-index="${index++}" class="highlight${tempSearch ? ' highlight2' : ''}" data-qid="${tempQid}">${tempContent}</span>`;
        }
        if (unMatchTempText.length > 0) {
          str += unMatchTempText;
          // if (trim(unMatchTempText).length > 0) {
          index++;
          // }
          unMatchTempText = '';
          // str += `<span data-index="${index++}">${unMatchTempText}</span>`;
        }
        return str;
      });
      console.log('getReadModeVideoStrings', Date.now() - start);
      return res;
    }
  }
});

function getMatchQids(renderInfo: RenderInfo) {
  const keys: (number | string)[] = [];
  for (const key in renderInfo.questionCountMap) {
    if (renderInfo.questionCountMap[key] >= renderInfo.relatedQuestionCount) {
      keys.push(key);
    }
  }
  return keys;
}
