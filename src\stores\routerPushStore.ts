import { defineStore } from "pinia"
import { ref } from "vue"
export const useRouterPushStore = defineStore("routerPushStore", () => {
  const data = ref<any>()
  const toOpenQuesId = ref<string>()
  
  // 数据
  const setData = (d: any) => {
    data.value = d
  }
  const getData = () => {
    return data.value
  }
  // 消息打开的问题id
  const setToOpenQuesId = (id: string) => {
    toOpenQuesId.value = id
  }
  const getToOpenQuesId = () => {return toOpenQuesId.value}
  return {
    setData,
    getData,
    setToOpenQuesId,
    getToOpenQuesId,
  }
})
