<script setup lang="ts">
import { watch, ref } from 'vue';
import type { imgUrl } from '@/utils/type';
import MyVideoPlayer from './myVideoPlayer.vue';
import { getM3U8Key } from '@/apis/path/cosBackend';
import { useRoute } from 'vue-router';
// @ts-ignore
import COS from 'cos-js-sdk-v5';

const route = useRoute();
const props = defineProps({
  video: {
    type: String,
    required: true
  },
  cover: {
    type: Object as () => imgUrl,
    required: true
  },
  projectId: {
    type: String,
    required: true
  }
});
const curPrjId = ref();
const videoSrc = ref();
const videoCut = ref();
const m3u8Key = ref();
const videoCover = ref<imgUrl>({
  echoUrl: '',
  commUrl: ''
});
const getVideoCutFromCos = () => {
  // 初始化COS时需要提供必要参数
  const defaultCosConfig = {
    SecretId: '',
    SecretKey: ''
  };
  const cos = new COS(defaultCosConfig);
  
};

const getVideoFromCos = (data: any) => {
  const cos = new COS({
        SecretId: data.TmpSecretId,
        SecretKey: data.TmpSecretKey,
        ExpiredTime: data.ExpiredTime,
        StartTime: data.StartTime,
        SecurityToken: data.SecurityToken
  });
  cos.getObjectUrl(
      {
        Bucket: import.meta.env.VITE_COS_BUCKET_NAME, // 存储桶，必须字段
        Region: import.meta.env.VITE_COS_REGION,
        Domain: import.meta.env.VITE_COS_DOMAIN,
        Key: props.video
      },
      function (err, data) {
        if (err) {
          console.error(err);
        } else {
          videoSrc.value = data.Url + import.meta.env.VITE_COS_AFTER;
        }
      }
    );
  
};

const percentage = ref(0);
const polling4resKey = () => {
  getM3U8Key().then((res) => {
    //成功了就执行一次
    if (res.success) {
      // @ts-ignore
      let timeInterval: NodeJS.Timer | null = null;
      // videoSrc.value = res.data.m3u8Key;
      getVideoFromCos(res.data);
      // @ts-ignore
      clearInterval(timeInterval);
    }
    //失败了就进入轮询
    else {
      // @ts-ignore
      let timeInterval: NodeJS.Timer | null = null;
      timeInterval = setInterval(() => {
        getM3U8Key().then((res) => {
          if (res.success) {
            // @ts-ignore
            let timeInterval: NodeJS.Timer | null = null;
            // videoSrc.value = res.data.m3u8Key;
            getVideoFromCos(res.data);
            // @ts-ignore
            clearInterval(timeInterval);
          } else {
            percentage.value = res.data.progress;
          }
          // 如果离开当前页面也要停止轮询
          if (!route.path.includes('step3')) {
            // @ts-ignore
            clearInterval(timeInterval);
          }
        });
      }, 5000);
      //!important 需要定义退出轮询的条件，注意边界情况，如返回上一步
    }
  });
};
watch(
  () => props,
  (newVal) => {
    videoCover.value = { ...newVal.cover };
    m3u8Key.value = newVal.video;
    curPrjId.value = newVal.projectId;
    if (newVal.video) {
      getVideoCutFromCos();
      polling4resKey();

      // 以下是直接播MP4的逻辑（特快，怀疑调取了本地暂存的视频文件）
      // 以后可能会改成轮询后端（上边），因为这样可以保证champaign的视频一定是处理完毕的，属于产品设计的逻辑
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div class="preview-wrapper">
    <div class="video-container">
      <!-- {{ videoSrc }} -->
      <template v-if="videoSrc">
        <my-video-player :video-src="videoSrc"></my-video-player>
      </template>
      <template v-else>
        <img class="video-cut" :src="videoCut" />
        <div style="position: absolute; bottom: 130px; left: 250px">
          <!-- <el-progress type="dashboard" :percentage="percentage">
            <template #default="{ percentage }">
              <span class="percentage-label">后台已处理</span>
              <span class="percentage-value">{{ percentage }}%</span>
            </template>
          </el-progress> -->
        </div>
      </template>
    </div>
    <div class="cover-container">
      <img v-if="videoCover.echoUrl" class="pic" :src="videoCover.echoUrl" />
      <el-empty v-else description="暂无封面" />
    </div>
  </div>
</template>

<style scoped>
.preview-wrapper {
  /*display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 360px;*/
  width: 1240px;

  .video-container {
    /*display: flex;
    justify-content: center;
    align-items: center;*/
    width: 800px;
    height: 480px;
    background-color: var(--color-nosrc);
    margin: 0 auto;
    margin-bottom: 15px;

    /* .cover1 {
      position: relative;
      width: 640px;
      height: 360px;
    } */

    .video-cut {
      height: 100%;
      width: 100%;
    }

    /* .cover1::before {
      content: " ";
      position: absolute;
      top: 0;
      left: 0;
      width: 640px;
      height: 360px;
      /*最后一个参数是半透明度，可以透过调整0-1的数值，调整到满意的透明度*/
    /* background-color: rgba(0, 0, 0, 0.5);
  } */

    .myicon {
      position: absolute;
      /*cursor: pointer;*/
      height: 30px;
    }
  }

  .cover-container {
    margin-left: 20px;
    width: 800px;
    height: 600px;
    margin: 0 auto;

    .pic {
      width: 800px;
      height: 600px;
    }
  }
}
</style>
