//划词
export enum qMode {
  necessary = 1,
  refer = 2
}

export enum qType {
  what = 1,
  why = 2,
  how = 3,
  open = 4
}
export const qTypeDict: { [key: string]: number } = {
  是什么: qType.what,
  为什么: qType.why,
  怎么做: qType.how,
  开放性问题: qType.open
};

export const qModeDict: { [key: string]: number } = {
  必要问题: qMode.necessary,
  参考问题: qMode.refer
};

export enum QuestionType {
  what = 1,
  why = 2,
  how = 3,
  open = 4
}

export const qTypeMap = new Map([
  [QuestionType.what, '是什么'],
  [QuestionType.why, '为什么'],
  [QuestionType.how, '怎么做'],
  [QuestionType.open, '开放性问题']
]);
