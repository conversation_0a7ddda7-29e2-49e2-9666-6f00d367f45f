<!-- 当用户鼠标悬浮在目标元素上时，会显示一个弹出框 -->
<template>
  <el-popover
    trigger="hover"
    :virtual-ref="referenceElement"
    placement="top"
    virtual-triggering
    popper-class="my-content"
  >
    <span class="container">
      <div class="ck-content container-content" v-html="content"></div>
    </span>
  </el-popover>
</template>

<script setup lang="ts">
import hljs from "highlight.js";
import { ref, watch } from "vue";

const referenceElement = ref<HTMLElement | null>(null);

const model = defineModel<HTMLElement | null>();
const content = ref("");

watch(
  () => model.value,
  (newVal) => {
    if (newVal) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(
        newVal?.getAttribute("thumbnail") as string,
        "text/html"
      );
      const codeElement = doc.querySelector("code");
      if (codeElement) {
        codeElement.classList.add("hljs");
        hljs.highlightElement(codeElement);
        content.value = doc.body.innerHTML;
      } else {
        content.value = newVal?.getAttribute("thumbnail") as string;
      }
      referenceElement.value = newVal;
    }
  }
);

// @ts-ignore
const handler = (event: Event) => {
  model.value = null;
};
</script>

<style lang="less" scoped>
// .content {
//   position: relative;
//   width: 150px;
//   //   overflow: hidden;
//   background: var(--el-bg-color-overlay);
//   border: 1px solid var(--el-border-color-light);
//   box-shadow: var(--el-box-shadow-light);
//   border-radius: var(--el-popover-border-radius);
//   &::before {
//     content: '';
//     position: absolute;
//     z-index: -1;
//     top: -15px;
//     left: calc(50% - 15px);
//     width: 30px;
//     height: 30px;
//     transform: rotateZ(45deg);
//     // background-color: red;
//     // border: 1px solid var(--el-border-color-light);
//     box-shadow: var(--el-box-shadow-light);
//     border-radius: var(--el-popover-border-radius);
//   }
// }
</style>
<style>
.el-popover.my-content {
  width: max-content !important;
  max-width: 600px !important;
  max-height: 400px !important;
  display: flex;

  .container {
    /* max-width: 600px; */
    overflow: scroll;
    &::-webkit-scrollbar {
      width: 5px;
      height: 5px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 6px;
      height: 50px;
    }
    .container-content {
      display: block;
      width: max-content;
    }
  }
}
</style>
