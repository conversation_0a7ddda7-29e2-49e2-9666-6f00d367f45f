<script setup lang="ts">
import { ref } from "vue"
import {
  prjForm,
  qMode,
  qType,
  qWeight,
  ansType,
  ansMode,
  qModeDict,
  qTypeDict,
  qWeightDict,
  answerTypeDict,
  ansModeDict,
} from "@/utils/constant"
import { Search, Close } from "@element-plus/icons-vue"
import { ExerciseType, ExerciseTypeWithAllDict } from "@/utils/constant"

const curQType = ref<qType>(qType.default)
const curQMode = ref<qMode>(qMode.default)
const curQWeight = ref<qWeight>(qWeight.default)
const curAnsType = ref<ansType>(ansType.default)
const curAnsMode = ref<ansMode>(ansMode.default)

const selectedForm = ref<prjForm>(prjForm.all)
const searchKey = ref("")
const searchKey4Ans = ref("")
const searchKey4Prj = ref("")
const emit = defineEmits([
  "select",
  "search",
  "setcur",
  "searchList",
  "selectStatus",
  "exercise",
])
const props = defineProps({
  placeholder: {
    type: String,
    default: "请输入",
  },
  needLeftText: {
    type: Boolean,
    default: false,
  },
  needCurForm: {
    type: Boolean,
    default: false,
  },
  needQuesWeight: {
    type: Boolean,
    default: false,
  },
  needQuesNess: {
    type: Boolean,
    default: false,
  },
  needQuesType: {
    type: Boolean,
    defauld: false,
  },
  needSearch: {
    type: Boolean,
    default: false,
  },
  needSearchAns: {
    type: Boolean,
    default: false,
  },
  needSearchPrj: {
    type: Boolean,
    default: false,
  },
  needAnsMode: {
    type: Boolean,
    default: false,
  },
  needAnsType: {
    type: Boolean,
    default: false,
  },
  needExerType: {
    type: Boolean,
    default: false,
  },
  needExerSearch: {
    type: Boolean,
    default: false,
  },
})
// 选择项目类型 全部||文稿||视频
const select = (select: prjForm) => {
  if (select) {
    selectedForm.value = select
  }
  emit("select", selectedForm.value)
}
// 搜索
const search = () => {
  emit("search", searchKey.value)
}

// 选择发布状态
const changeStatus = (select: number) => {
  emit("selectStatus", select)
}
// 搜索
const searchList = () => {
  emit("searchList", searchKey4Ans.value, searchKey4Prj.value)
}
const handleDelete = () =>{
  searchKey4Ans.value = ''
  form4Exercise.value.stem = ''
  searchKey.value = ''
  searchKey4Prj.value = ''
}
// 习题表单
const form4Exercise = ref({
  type: 0,
  stem: "",
})
// 提交筛选习题表单
const handleExercise = () => {
  emit("exercise", form4Exercise.value)
}
// 筛选
const changeQuesList = () => {
  let curSelect = {
    curQType: curQType.value,
    curQMode: curQMode.value,
    curQWeight: curQWeight.value,
    curAnsType: curAnsType.value,
    curAnsMode: curAnsMode.value,
  }
  emit("setcur", curSelect)
}
</script>

<template>
  <span class="switchContainer">
    <span class="btnGroup">
      <span class="left-btn" v-if="props.needCurForm">
        <span class="option" style="width: 60px" :class="selectedForm == prjForm.all ? 'active' : ''"
          @click="select(prjForm.all)">
          <img style="margin-right: 10px; margin-top: 2px" src="@/assets/images/prjForm/u319.svg" />
          全部
        </span>
        <span class="option" :class="selectedForm == prjForm.video ? 'active' : ''" @click="select(prjForm.video)">
          <img style="margin-right: 10px; margin-top: 2px" src="@/assets/images/prjForm/u345.svg" />
          视频项目
        </span>
        <span class="option" :class="selectedForm == prjForm.text ? 'active' : ''" @click="select(prjForm.text)">
          <img style="margin-right: 10px; margin-top: 2px" src="@/assets/images/prjForm/u342.svg" />
          文稿项目
        </span>
      </span>
      <!--
      <span class="left-text" v-if="$props.needLeftText">
        <span>筛选</span>
      </span>-->
    </span>

    <span class="rightContent">
      <span class="select-group">
        <!-- exercise -->
        <el-select @change="handleExercise" v-model="form4Exercise.type" placeholder="全部问题类型" style="margin-right: 10px"
          class="select" v-if="props.needExerType">
          <el-option v-for="(value, key) in ExerciseTypeWithAllDict" :key="key" :label="key" :value="value"
            :class="form4Exercise.type === value ? 'highlight' : ''">
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input class="input" v-model="form4Exercise.stem" @keydown.enter="handleExercise" @blur="handleExercise"
          :placeholder="props.placeholder" style="margin-right: 10px" v-if="props.needExerSearch">
          <template #suffix>
            <el-icon class="close-icon" style="cursor: pointer; margin-right: 5px;" @click="handleDelete"
              ><Close
            /></el-icon>
            <el-icon class="btn el-input__icon" @click="handleExercise">
              <Search />
            </el-icon>
          </template>
        </el-input>
        <!-- detail -->
        <el-select @change="changeQuesList" v-model="curQType" placeholder="全部问题类型" style="margin-right: 10px"
          class="select" v-if="$props.needQuesNess">
          <el-option v-for="(value, key) in qTypeDict" :key="key" :label="key" :value="value"
            :class="curQType === value ? 'highlight' : ''">
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select @change="changeQuesList" v-model="curQMode" placeholder="全部问题属性" style="margin-right: 10px"
          class="select" v-if="$props.needQuesNess">
          <el-option v-for="(value, key) in qModeDict" :key="key" :label="key" :value="value"
            :class="curQMode === value ? 'highlight' : ''">
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select @change="changeQuesList" v-model="curQWeight" placeholder="全部公开性" class="select"
          style="margin-right: 10px" v-if="$props.needQuesWeight">
          <el-option v-for="(value, key) in qWeightDict" :key="key" :label="key" :value="value"
            :class="curQWeight === value ? 'highlight' : ''">
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>

        <!--
        <el-select
          @change="changeQuesList"
          v-model="curAnsType"
          placeholder="全部答案状态"
          class="select"
          style="margin-right: 10px"
          v-if="$props.needAnsType"
        >
          <el-option
            v-for="(value, key) in answerTypeDict"
            :key="key"
            :label="key"
            :value="value"
            :class="curAnsType === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>-->
        <el-input class="input" v-model="searchKey" @keydown.enter="search" @blur="search" :placeholder="props.placeholder"
          style="margin-right: 10px" v-if="$props.needSearch">
          <template #suffix>
            <el-icon class="close-icon" style="cursor: pointer; margin-right: 5px;" @click="handleDelete"
              ><Close
            /></el-icon>
            <el-icon class="btn el-input__icon" @click="search">
              <Search />
            </el-icon>
          </template>
        </el-input>
        <!-- answer -->
        <el-select @change="changeStatus" v-model="curAnsMode" placeholder="全部答案状态" class="select"
          style="margin-right: 10px" v-if="$props.needAnsMode">
          <el-option v-for="(value, key) in ansModeDict" :key="key" :label="key" :value="value"
            :class="curAnsMode === value ? 'highlight' : ''">
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>

        <el-input class="input" v-model="searchKey4Ans" @keydown.enter="searchList" @blur="searchList" placeholder="请输入答案解释"
          style="margin-right: 10px" v-if="$props.needSearchAns">
          <template #suffix>
            <el-icon class="close-icon" style="cursor: pointer; margin-right: 5px;" @click="handleDelete"
              ><Close
            /></el-icon>
            <el-icon class="btn el-input__icon" @click="searchList">
              <Search />
            </el-icon>
          </template>
        </el-input>
        <el-input class="input" v-model="searchKey4Prj" @keydown.enter="searchList" @blur="searchList" :placeholder="props.placeholder"
          style="margin-right: 10px" v-if="$props.needSearchPrj">
          <template #suffix>
            <el-icon class="close-icon" style="cursor: pointer; margin-right: 5px;" @click="handleDelete"
              ><Close
            /></el-icon>
            <el-icon class="btn el-input__icon" @click="searchList">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </span>
    </span>
  </span>
</template>

<style scoped>
.switchContainer {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  width: 100%;

  &::after {
    content: "";
    height: 1px;
    width: 100%;
    background-color: var(--color-line);
    position: absolute;
    bottom: 0;
  }

  .btnGroup {
    display: flex;
    align-items: center;

    .left-btn {
      display: flex;
      align-items: center;
    }

    .left-text {
      margin-left: 10px;
      font-weight: bold;
    }

    .option {
      width: 105px;
      /* 这个数字没有严丝合缝的遵循原型，因为font-weight变化时宽度会变，如果严格按照原型，文稿按钮会被顶的左右移动 */
      display: flex;
      align-items: center;
      margin-left: 30px;
      cursor: pointer;
      font-size: 16px;
      color: var(--color-grey);
      font-family: var(--font-family-text);
    }

    .active {
      color: var(--color-primary);
      font-weight: 600;
    }
  }

  .rightContent {
    .input {
      width: 200px;
    }

    .btn {
      cursor: pointer;
    }

    &:deep(.el-input__wrapper) {
      --el-input-focus-border-color: var(--color-primary);
      border-radius: 0px;
    }
  }
}

.select-group {
  .select {
    /*--el-color-primary: var(--color-primary);*/
    width: 180px;

    &:deep(.el-input) {
      min-height: 35px;
      --el-input-height: 35px;
      line-height: 35px;
    }
  }
}

:deep(.el-select__wrapper) {
  min-height: 35px;
}

:deep(.el-input__wrapper) {
  min-height: 35px;
}
</style>
