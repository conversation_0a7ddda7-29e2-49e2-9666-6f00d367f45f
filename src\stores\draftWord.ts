import { defineStore } from 'pinia';
import type { RenderInfo } from '@/types/word';
import { toRaw } from 'vue';

export const useDraftWordStoreV2 = defineStore('draftWordV2', {
  state: () => ({
    regString: '',
    renderInfoList: [] as RenderInfo[],
    renderInfoIndexes: [] as number[],
  }),

  actions: {
    resetRenderInfoList(clearQid: boolean = true, clearSearch: boolean = true) {
      const renderInfoList = toRaw(this.renderInfoList);
      // const tempRenderList = this.renderInfoList.filter((item) => {
      //   return item.qids.length > 0 || item.search;
      // });
      // tempRenderList.forEach((item) => {
      //   if (clearQid) {
      //     item.qids = [];
      //   }
      //   if (clearSearch) {
      //     item.search = false;
      //   }
      // });
      renderInfoList.forEach((item) => {
        if (clearQid) {
          item.questionCountMap = {};
        }
        if (clearSearch) {
          item.searchCount = 0;
        }
      });
    },
    getAskModeDraftString(): string {
      const start = Date.now();
      const renderInfoList = toRaw(this.renderInfoList);
      const res = renderInfoList
        .map((item) => {
          return item.content;
        })
        .join('');
      return res;
    },
    getReadModeDraftString(): string {
      const start = Date.now();
      let tempQid = null;
      let tempContent = null;
      let tempSearch = null;
      let unMatchTempText = '';
      let str = '';
      let index = 0;
      const renderInfoList = toRaw(this.renderInfoList);
      for (let i = 0; i < renderInfoList.length; ++i) {
        const renderInfo = renderInfoList[i];
        const matchQids = getMatchQids(renderInfo);
        if (matchQids.length || renderInfo.searchCount >= renderInfo.relatedSearchCount) {
          if (unMatchTempText.length > 0) {
            str += unMatchTempText;
            // if (trim(unMatchTempText).length > 0) {
            index++;
            // }
            unMatchTempText = '';
          }
          if (tempQid == null) {
            tempQid = matchQids.join(',');
            tempContent = renderInfo.content;
            tempSearch = renderInfo.searchCount >= renderInfo.relatedSearchCount;
          } else if (
            tempQid == matchQids.join(',') &&
            tempSearch == renderInfo.searchCount >= renderInfo.relatedSearchCount
          ) {
            tempContent += renderInfo.content;
          } else {
            if (tempQid.length > 0) {
              str += `<span data-index="${index++}" class="highlight" data-qid="${tempQid}">${tempContent}</span>`;
            } else {
              str += `<span>${tempContent}</span>`;
            }
            tempQid = matchQids.join(',');
            tempContent = renderInfo.content;
            tempSearch = renderInfo.searchCount >= renderInfo.relatedSearchCount;
          }
        } else {
          if (tempQid != null) {
            if (tempQid.length > 0) {
              if (tempSearch) {
                str += `<span data-index="${index++}" class="highlight highlight2" data-qid="${tempQid}">${tempContent}</span>`;
              } else {
                str += `<span data-index="${index++}" class="highlight" data-qid="${tempQid}">${tempContent}</span>`;
              }
            } else {
              str += `<span class="highlight2">${tempContent}</span>`;
            }
          }
          if (renderInfo.isTag) {
            if (unMatchTempText.length > 0) {
              str += unMatchTempText;
              // if (trim(unMatchTempText).length > 0) {
              index++;
              // }
              unMatchTempText = '';
            }
            str += renderInfo.content;
          } else {
            // 检查是否是公式元素
            if (renderInfo.content.includes('class="equation"') || renderInfo.content.includes('class="inline-equation"')) {
              // 为公式元素添加data-qid属性
              str += `<span data-index="${index++}" class="highlight" data-qid="">${renderInfo.content}</span>`;
            } else {
              unMatchTempText += renderInfo.content;
            }
          }
          tempQid = null;
          tempContent = null;
          tempSearch = null;
        }
      }
      if (tempQid != null) {
        str += `<span data-index="${index++}" class="highlight${tempSearch ? ' highlight2' : ''}" data-qid="${tempQid}">${tempContent}</span>`;
      }
      if (unMatchTempText.length > 0) {
        str += unMatchTempText;
        // if (trim(unMatchTempText).length > 0) {
        index++;
        // }
        unMatchTempText = '';
        // str += `<span data-index="${index++}">${unMatchTempText}</span>`;
      }
      return str;
    }
  }
});

function getMatchQids(renderInfo: RenderInfo) {
  const keys: (number | string)[] = [];
  for (const key in renderInfo.questionCountMap) {
    if (renderInfo.questionCountMap[key] >= renderInfo.relatedQuestionCount) {
      keys.push(key);
    }
  }
  return keys;
}
