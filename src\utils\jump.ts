// 页面跳转
import { System } from '@/enums/system'
import router from '@/router'

// 跳转到登录页
export const jumpToLogin = (delayTime?: number) => {
  const time = delayTime ? delayTime : 500
  setTimeout(() => {
    window.open(
      `${import.meta.env.VITE_APP_YOUTH_URL}/login?type=2&&redirect=${
        import.meta.env.VITE_DUTCH_URL
      }`,
      '_self'
    )
  }, time)
}

// 系统路由跳转
export const routerPushSystem = (system: System) => {
  switch (system) {
    case System.champaign:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_CHAMP_URL, '_self')
      break
    case System.youthfountain:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_YOUTH_URL, '_self')
      break
    case System.champadmin:
      // @ts-ignore
      break
    case System.wellerman:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_WELLER_URL, '_self')
      break
    case System.dutchman:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_DUTCH_URL, '_self')
      break
    case System.mayflower:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_FLOWER_URL, '_self')
      break
    case System.blackpearl:
      // @ts-ignore
      window.open(import.meta.env.VITE_APP_BLACK_URL, '_self')
  }
}
