// 内容类型常量 文稿类型document - 2  视频类型 video - 1
export enum ContentType {
  document = 2,
  video = 1,
}

export enum WorkerType {
  draft = 1,
  video = 2,
  exercise = 3,
  proof = 4,
}

// 学习类型
export enum LearnType {
  project = 2, // 案例学习
  klgExplain = 1, //知识讲解
  klgTest = 3, // 知识测评
  area = 4, // 领域学习
}

// 浏览记录时间类型
export enum HistoryType {
  all,
  month,
  week,
  today,
}

// 购买状态
export enum BuyStatus {
  nobuy,
  bought,
}
// 是否是会员商品
// 目前的会员商品只有领域
// 非会员商品包括（案例，讲解，测评。。。。
export enum GoodsType {
  project,
  vip,
}
export const cardTypeMap = new Map([
  [3, "月卡"],
  [2, "季卡"],
  [1, "年卡"],
]);

//划词
export enum qMode {
  default = -1,
  ness = 1, // 必要模式
  refer = 2, // 参考模式
}

export enum qType {
  default = -1,
  what = 1,
  why = 2,
  how = 3,
  open = 4,
}
export enum qWeight {
  default = -1,
  personal = 1,
  open = 2,
}
export enum ansType {
  default = -1,
  noans = 0, // 无答案
  partpub = 1, // 部分发布
  allpub = 2, // 全发布
  nopub = 3, // 未发布
}
export enum taskCompleteType {
  pending = 1,
  assigned = 2,
  executed = 3,
  completed = 4,
  returned = 5,
  executing = 6,
}
export enum wordMode {
  reading = 0, // 展示模式
  questioning = 1, // 提问模式
}

export enum ansMode {
  default = -1,
  pending = 0, // 未处理
  processing = 1, // 处理中
  completed = 2, // 处理完
}
export const taskCompleteTypeDict: { [key: string]: number } = {
  待分配: taskCompleteType.pending,
  已分配: taskCompleteType.assigned,
  已执行: taskCompleteType.executed,
  已完成: taskCompleteType.completed,
  已退回: taskCompleteType.returned,
  执行中: taskCompleteType.executing,
};
export const ansModeDict: { [key: string]: number } = {
  全部答案状态: ansMode.default,
  未处理: ansMode.pending,
  处理中: ansMode.processing,
  处理完: ansMode.completed,
};

export const wordModeDict: { [key: string]: number } = {
  展示模式: wordMode.reading,
  提问模式: wordMode.questioning,
};
export const qTypeDict: { [key: string]: number } = {
  全部问题类型: qType.default,
  是什么: qType.what,
  为什么: qType.why,
  怎么做: qType.how,
  开放性问题: qType.open,
};

export const qModeDict: { [key: string]: number } = {
  全部问题属性: qMode.default,
  必要问题: qMode.ness,
  参考问题: qMode.refer,
};

export const qWeightDict: { [key: string]: number } = {
  全部公开性: qWeight.default,
  个人问题: qWeight.personal,
  公开问题: qWeight.open,
};

export const answerTypeDict: { [key: string]: number } = {
  全部答案状态: ansType.default,
  全发布: ansType.allpub,
  部分发布: ansType.partpub,
  无答案: ansType.noans,
  未发布: ansType.nopub,
};

export enum prjForm {
  video = "1",
  text = "2",
  all = "3", // getList时传空为拿全部
}

export enum prjType {
  default = "",
  klg = "1",
  prj = "2",
  exam = "3",
}

export const typeDict: { [key: string]: string } = {
  全部类型: "",
  知识讲解: "1",
  案例学习: "2",
  知识测评: "3",
  领域讲解: "4",
};
export const typeDict_noDefault: { [key: string]: string } = {
  知识讲解: "1",
  案例学习: "2",
  知识测评: "3",
  领域讲解: "4",
};

export const sortDict: { [key: string]: number } = {
  时间降序: 0,
  时间升序: 1,
};

export enum examType {
  blank = "1",
  select = "2",
  judge = "3",
  qa = "4",
}
export const examTypeDict: { [key: string]: string } = {
  填空题: "1",
  选择题: "2",
  判断题: "3",
  问答题: "4",
};

export const formDict: { [key: string]: string } = {
  视频: "1",
  文稿: "2",
};
export enum prjState {
  default = "",
  draft = "0",
  wait2examine = "1",
  examining = "2",
  back = "3",
  withdraw = "4",
  publish = "5",
  delete = "6",
}

export const pageList = [
  {
    title: "我的项目",
    tag: "myproject",
    router: "/home",
    icon: "@/assets/images/lefter/u378.svg",
    subPageList: [
      { title: "我的项目", tag: "myproject", router: "/home" },
      { title: "项目详情", tag: "myproject", router: "/prjdetail" },
    ],
  },
  {
    title: "我的习题",
    tag: "myproject",
    router: "/exercise",
    icon: "@/assets/images/lefter/u384.svg",
    subPageList: [
      { title: "我的习题", tag: "myproject", router: "/exercise" },
      { title: "习题详情", tag: "myproject", router: "/exerdetail" },
    ],
  },
  {
    title: "问题消息",
    tag: "myproject",
    router: "/question",
    icon: "@/assets/images/lefter/u384.svg",
    subPageList: [],
  },
  {
    title: "维护标签",
    tag: "myproject",
    router: "/tag",
    icon: "@/assets/images/lefter/u392.svg",
    subPageList: [],
  },
  {
    title: "答案发布",
    tag: "myproject",
    router: "/answer",
    icon: "@/assets/images/lefter/u392.svg",
    subPageList: [],
  },
];

export enum ExerciseType {
  all = 0,
  single = 1,
  multi = 2,
  blank = 3,
  judge = 4,
}
export const ExerciseTypeWithAllDict: { [key: string]: number } = {
  全部类型: ExerciseType.all,
  单选: ExerciseType.single,
  多选: ExerciseType.multi,
  填空: ExerciseType.blank,
  判断: ExerciseType.judge,
};

export enum conditionState {
  draft,
  waitingpass,
  nowpassing,
  backbyother,
  backbyme,
  online,
  delete,
}
export const ExerciseTypeDict: { [key: string]: number } = {
  单选题: ExerciseType.single,
  多选题: ExerciseType.multi,
  填空题: ExerciseType.blank,
  判断题: ExerciseType.judge,
};

export enum belongProjectType {
  single = 1,
  multi = 2,
  blank = 3,
  judge = 4,
  video = 5,
  draft = 6,
}

export const belongProjectTypeDict: { [key: string]: number } = {
  单选题: belongProjectType.single,
  多选题: belongProjectType.multi,
  填空题: belongProjectType.blank,
  判断题: belongProjectType.judge,
  视频: belongProjectType.video,
  文稿: belongProjectType.draft,
};
