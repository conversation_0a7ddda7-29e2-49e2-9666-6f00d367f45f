import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
// @ts-ignore
import { fileURLToPath, URL } from "node:url";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import mkcert from "vite-plugin-mkcert";
import { codeInspectorPlugin } from 'code-inspector-plugin';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // commonjs() as any,
    vue(),
    AutoImport({
      imports: ["vue", "vue-router"],
      resolvers: [ElementPlusResolver({ importStyle: "sass" })],
    }),
    Components({
      resolvers: [ElementPlusResolver({ importStyle: "sass" })],
    }),
    mkcert(),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern",
        additionalData: `@use "@/assets/theme/index.scss" as *;`,
      },
    },
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    host: "0.0.0.0",
    port: 5675,
    proxy: {
      "/wellerman-service": {
        target: "http://**************:8001",
        changeOrigin: true,
      },
      "/dutchman-service": {
        target: "http://**************:8001", // 测试环境调试
        changeOrigin: true,
      },
      "/auth-service": {
        target: "http://**************:8001", // 测试环境调试
        changeOrigin: true,
      },
    },
  },
  // optimizeDeps: {
  //   // include: ["ckeditor5-custom-build", "@ckeditor/ckeditor5-vue"],
  // },
  // build: {
  //   commonjsOptions: {
  //     // include: [/ckeditor-super/, /ckeditor-vue/, /node_modules/],
  //   },
  // },
});
