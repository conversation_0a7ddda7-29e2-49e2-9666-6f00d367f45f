<script setup lang="ts">
import { inject, onMounted, ref } from "vue"
import MyUploadAdapter from "@/utils/ckEditorImgUploader"
import { defineModel } from "vue"

const Editor = inject("Editor") as any
const props = defineProps({
  disabled:{
    type: Boolean,
    default: false,
  },
  height: {
    type: Number,
    default: 150,
  }
});
const editorData = ref()
const editorConfig = {
  placeholder: "请输入",
  extraPlugins: [MyCustomUploadAdapterPlugin],
  height: props.height
}
const disable = ref()
const getData = () => {
  return editorData.value
}

const setData = (value: string, dis?: boolean) => {
  editorData.value = value
  disable.value = dis
  if (dis == undefined) {
    disable.value = false
  }
}
const model = defineModel()
function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader)
  }
}

onMounted(()=>{
  model.value+=' '
})
defineExpose({
  getData,
  setData,
})
</script>

<template>
  <ckeditor
    class="inlineEditor"
    :disabled="props.disabled"
    :editor="Editor.InlineEditorCustom"
    v-model="(model as string)"
    :config="editorConfig"
  >
  </ckeditor>
</template>

<style scoped>
.ck.ck-editor__editable_inline > :last-child {
  margin-bottom: 5px;
}

.ck.ck-editor__editable_inline > :first-child {
  margin-top: 5px;
}
.inlineEditor {
  /* 不知道为什么inline模式的边框不见了，手动设置了成了classic的边框颜色 */
  border: 1px solid #ccced1;
}
</style>
