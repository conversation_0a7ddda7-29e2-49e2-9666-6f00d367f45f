@use '@/theme-chalk/mixins/mixins.scss' as *;

@include b(info-panel-header) {
    width: inherit;
    height: $height__info-header;

    display: flex;
    justify-content: space-between;

    background-color: $background-color--primary;

    position: relative;

    @include e(identity) {
        padding-left: $padding-left__info-header-identity;
        font-size: $font-size--medium;
        font-family: $font-family-puhuiti-55;

        color: $color__font-secondary-header--rich;
        position: absolute;
        top: 39px;
    }

    @include e(link) {
        font-size: $font-size--tiny;
        font-family: $font-family-puhuiti-55;
        color: $color__primary;
        padding-top: 39px;

        &:hover {
            font-weight: bold;
            text-decoration: underline;
            cursor: pointer;
        }
    }

    position: relative;
    @include under_line;

    @include e(content) {
        display: flex;
        flex-direction: column;
        padding-left: $padding-left__info-header-identity;

        position: relative;
        width: 500px;
    }

    @include e(content-title) {
        font-family: $font-family-puhuiti-semibold;
        font-size: $font-size--big;
        color: $color__font-primary--dark;

        position: absolute;
        top: 10px;
    }

    @include e(content-detail) {
        font-family: $font-family-puhuiti-55;
        font-size: $font-size--tiny;
        color: $color__font-secondary-header--rich;

        position: absolute;
        top: 39px;
    }
}
