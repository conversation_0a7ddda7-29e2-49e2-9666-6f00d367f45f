<script setup lang="ts">
import MyTag from "./MyTag.vue"
import { ref, watch } from "vue"
import type { prjInfoType, tagType } from "@/utils/type"
import { typeDict, typeDict_noDefault } from "@/utils/constant"
import { processAllLatexEquations } from '@/utils/latexUtils';

const typeClass = ref("")
const props = defineProps({
  infoData: {
    type: Object as () => prjInfoType,
    required: true,
  },
})
const prjInfoData = ref<prjInfoType>({
  prjType: "",
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
})
watch(
  () => props.infoData,
  (newValue) => {
    // console.log('step2PrjInfo: watched\n' + JSON.stringify(newValue, null, 2));
    prjInfoData.value = { ...newValue }
    typeClass.value = "type_" + typeDict[prjInfoData.value.prjType]
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <div class="info-wrapper">
    <div class="floor">
      <span class="left-wrapper">
        <div class="headFloor">
          <span class="title ck-content" v-html="(prjInfoData.prjName)"></span>
          <span class="typeTip" :class="typeClass">{{
            prjInfoData.prjType
          }}</span>
        </div>
        <div class="aim ck-content" v-html="(prjInfoData.prjAim)"></div>
        <div class="general ck-content" v-html="(prjInfoData.prjGeneral)"></div>
        <div class="tagBar">
          <my-tag
            class="t"
            v-for="tag in prjInfoData.prjTagList"
            :tag-id="tag.areaCode"
            :key="tag.areaCode"
            type="tag"
            :deletable="false"
          >
            <el-tooltip
              popper-class="tooltip-width"
              :content="tag.title"
              raw-content
            >
              {{ tag.title }}
            </el-tooltip>
          </my-tag>
        </div>
      </span>
      <span class="right-wrapper">
        <img class="miniPic" :src="prjInfoData.prjCover.echoUrl" />
      </span>
    </div>
    <div
      class="targetBar"
      v-if="typeDict_noDefault[prjInfoData.prjType] !== '2'"
    >
      <div class="title">
        {{ typeDict[prjInfoData.prjType] == "1"|| typeDict[prjInfoData.prjType] == '4' ? "讲解" : "测评" }}目标
      </div>
      <div class="content">
        <my-tag
          class="t2"
          v-for="t in prjInfoData.prjTargetList"
          :tag-id="t.areaCode"
          :key="t.areaCode"
          type="target"
          :deletable="false"
        >
          <el-tooltip
            popper-class="tooltip-width"
            :content="t.title"
            raw-content
          >
            <span class="ck-content" v-html="t.title"></span>
          </el-tooltip>
        </my-tag>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.info-wrapper {
  display: flex;
  width: 100%;
  flex-direction: column;
  .floor {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 0 20px;
    border-bottom: 1px solid #dcdfe6;
    .left-wrapper {
      width: 901px;
      flex-direction: column;
      /*background-color: #1661AB;*/
      position: relative;
      .headFloor {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin-bottom: 15px;
        .title {
          font-size: 24px;
          font-weight: 600;
          margin-right: 10px;
          font-family: var(--font-family-info);
        }
        .typeTip {
          height: 24px;
          line-height: 24px;
          display: flex;
          justify-content: center;
          border-radius: 5px;
          border: 1px solid;
          padding-left: 13px;
          padding-right: 13px;
          font-size: 14px;
          font-weight: 400;
        }
      }
      .aim {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 15px;
        font-family: var(--font-family-info);
        word-break: break-all;
      }
      .general {
        font-size: 14px;
        font-family: var(--font-family-text);
        margin-bottom: 30px;
        word-break: break-all;
      }
      .tagBar {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        flex-wrap: wrap;
        position: absolute;
        bottom: 0;
      }
    }
    .right-wrapper {
      .miniPic {
        height: 180px;
        width: 239px;
        border-radius: 5px;
      }
    }
    &::after {
      content: "";
      height: 1px;
      width: 100%;
      background-color: var(--color-line);
      position: absolute;
      bottom: 0;
    }
  }
  .targetBar {
    background-color: #f2f2f2;
    display: flex;
    flex-direction: column;
    padding: 10px;
    margin-bottom: 10px;
    .title {
      font-size: 16px;
      line-height: 16px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .content {
      display: flex;
      align-items: flex-start;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
  .t {
    margin-right: 20px;
    margin-bottom: 10px;
  }

  .t2 {
    margin-right: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
  }
}
.type_1 {
  border-color: var(--color-explain);
  color: var(--color-explain);
}
.type_2 {
  border-color: var(--color-case);
  color: var(--color-case);
}
.type_3 {
  border-color: var(--color-exam);
  color: var(--color-exam);
}
.type_4 {
  border-color: var(--color-primary);
  color: var(--color-primary);
}
</style>
