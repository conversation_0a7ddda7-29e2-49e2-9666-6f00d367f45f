<script setup lang="ts">
import { onMounted, ref } from "vue";
import { Minus } from "@element-plus/icons-vue";

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: "target",
  },
  tagId: {
    type: String,
    required: true,
  },
  deletable: {
    type: Boolean,
    required: false,
    default: true,
  }
});
const emit = defineEmits(['delete']);
const handleDelete = () => {
  emit('delete', props.tagId);
}
onMounted(() => {
})
</script>

<template>
  <span :class="props.type">
    <span class="text">
      <slot></slot>
    </span>
    <span v-if="deletable && type != 'newTarget'" @click="handleDelete" class="btn">×</span>
    <span v-if="deletable && type == 'newTarget'" @click="handleDelete" class="nbtn">
      <div class="custom-button"></div>
    </span>
  </span>
</template>

<style scoped>
.target {
  height: 35px;
  width: 156px;
  border: 1px solid var(--color-primary);
  background-color: white;
  color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;

  .btn {
    color: var(--color-grey);
  }
}

.tag {
  height: 25px;
  width: 120px;
  border-radius: 10px;
  /*background-color: #9eb7e5;*/
  background-color: #f2f2f2;
  /*color: white;*/
  display: flex;
  justify-content: center;
  align-items: center;
}

.text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.btn {
  margin-left: 5px;
  cursor: pointer
}

.newTarget {
  height: 30px;
  line-height: 30px;
  border: 1px solid #dcdfe6;
  position: relative;
}

.nbtn {
  cursor: pointer;
  position: absolute;
  right: -25px
}

.custom-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: var(--color-primary);
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  transition: background-color 0.2s;
  position: relative;
}

.custom-button::before {
  content: '';
  position: absolute;
  width: 5px;
  height: 2px;
  background-color: white;
  border-radius: 1px;
}

.klgTag {
  height: 25px;
  width: 120px;
  border-radius: 10px;
  /*background-color: #9eb7e5;*/
  background-color: #f2f2f2;
  /*color: white;*/
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>