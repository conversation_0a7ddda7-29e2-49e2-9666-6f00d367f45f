<script setup lang="ts">
import ExerInfoBlock from "@/components/ExerInfoBlock.vue"
import questionListTableForExer from "@/views/prjdetail/components/questionListTableForExer.vue"
import modeTypeSwitcher from "@/views/prjdetail/components/modeTypeSwitcher.vue"
import QuestionDrawer from "@/components/QuestionDrawer.vue"
import AnswerDrawer from "@/components/AnswerDrawer.vue"
import MulQuestionList from "../prjdetail/components/MulQuestionList.vue"

import router from "@/router"
import { onMounted, onUnmounted, provide, ref, triggerRef, watch } from "vue"
import { getExerDetailApi } from "@/apis/path/exercise"
import { getQuestionList } from "@/utils/lineWord2V2"
import { ElMessage } from "element-plus"
import { useRoute } from "vue-router"
import { useRouterPushStore } from "@/stores/routerPushStore"
import { emitter } from "@/utils/emitter"
import { Event } from "@/types/event"
import { Exercise } from "@/types/exercise"
import { processAllLatexEquations } from "@/utils/latexUtils"
import { renderMarkdown } from "@/utils/markdown";
import QuestionDialog from '@/components/QuestionDialog.vue'
import AnswerDialog from "@/components/AnswerDialog.vue"
import GenerateDialog from "@/components/GenerateDialog.vue"
import { useDialogStore } from "@/stores/dialog"
import { downloadGenerateQuestion, getGenerateCurrent } from "@/apis/path/prjdetail"

const dialogStore = useDialogStore()
const isQuesGenerating = ref(false)

const routerPushStore = useRouterPushStore()
const curHeaderMode = ref(0)
const questionDrawerRef = ref()
const answerDrawerRef = ref()
const mulQuesionListRef = ref()
const toOpenQuesId = ref()
const exerciseId = ref()
const route = useRoute()
const uniqueCode = route.query.uniqueCode as string
provide("toOpenQuestion", toOpenQuesId)

const questionOnwer = ref({
  exerciseId: "",
  onwerType: 0,
})
const exercise = ref<Exercise>({
  type: 0,
  stem: "",
  content: [],
  answer: "",
  explanation: ""
})

// 处理转换header的mode
const handleChangeHeader = (newHeaderMode: number) => {
  curHeaderMode.value = newHeaderMode
  sessionStorage.setItem('mode', `${curHeaderMode.value}`)
}
// 获取习题详情
const getExerciseDetail = async () => {
  exercise.value = await routerPushStore.getData()
  console.log("exercise.value", exercise.value)
}
const handleClickWord = (el: HTMLElement) => {
  console.log("el", el)
  // 如果元素为空或没有data-qid属性，直接返回
  if (!el) return
  const id = (el as HTMLElement).getAttribute("data-qid")
  if (!id) {
    console.log("元素没有data-qid属性")
    return
  }

  const idList = id.split(",")
  if (idList.length > 1) {
    getQuestionList(id).then((questionList) => {
      const query = {
        questionList: questionList,
        element: el,
      }
      emitter.emit(Event.SHOW_MUL_LIST, query)
    })
  } else if (idList.length === 1) {
    emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: parseInt(id) })
  }
}

const openGenerateDialog = () => {
  dialogStore.generateDialogVisible = true
  dialogStore.belongProjectType = exercise.value.type
}

const quesGenerating = ref(false)
const taskId = ref('')
const downloadFile = (content: any) => {
  // 让Blob根据内容自动推断类型（保留空对象）
  const blob = new Blob([content], {});

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  // 保留download属性但设为空，强制触发下载而非跳转
  link.download = '';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

const downloadGenerateQues = async () => {
  try {
    console.log('开始下载，taskId:', taskId.value)
    
    // 获取文件blob响应
    const res: any = await downloadGenerateQuestion(taskId.value)
    console.log('下载响应类型:', typeof res)
    console.log('下载响应:', res)
    console.log('是否为Blob:', res instanceof Blob)
    console.log('响应大小:', res instanceof Blob ? res.size : 'N/A')

    // 检查响应是否为blob
    if (res instanceof Blob) {
      // 使用默认文件名
      const filename = `生成问题_${taskId.value}.xlsx`
      console.log('文件名:', filename)
      
      // 创建blob URL
      const blobUrl = URL.createObjectURL(res)
      console.log('Blob URL:', blobUrl)
      
      // 创建下载链接
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename
      
      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理blob URL
      URL.revokeObjectURL(blobUrl)
      
      console.log('文件下载成功')
    } else {
      console.error('响应不是blob类型:', typeof res)
      console.error('响应内容:', res)
    }
  } catch (error) {
    console.error('下载失败:', error)
    console.error('错误详情:', error.response || error)
    // 可以添加错误提示UI
  }
}
const errorMessage = ref(false)

let timer = null
watch(
  () => isQuesGenerating.value,
  () => {
    if (isQuesGenerating.value) {
      errorMessage.value = false
      timer = setInterval(async () => {
        const res: any = await getGenerateCurrent(uniqueCode)
        if (res.success) {
          if (res.data.status == 1) {
            //生成问题结束之后需要请求新的问题列表
            //通知table组件进行请求
            emitter.emit(Event.GENERATE_SUCCESS)
            //成功之后quesGenerating.value为true，用于下载问题
            quesGenerating.value = true
            isQuesGenerating.value = false
            taskId.value = res.data.taskId
            clearInterval(timer)
          } else if (res.data.status == 2) {
            clearInterval(timer)
            isQuesGenerating.value = false
            quesGenerating.value = false
            errorMessage.value = true
          }
        }
        //每五秒请求一次
      }, 5000)
    }
  }
)

watch(
  () => route.query.uniqueCode,
  () => {
    if (route.query.uniqueCode) {
      exerciseId.value = route.query.uniqueCode.toString()
      questionOnwer.value.exerciseId = exerciseId.value
      getExerciseDetail()
    }
  },
  { deep: true, immediate: true }
)
onMounted(async () => {
  emitter.on(Event.CLICK_WORD, handleClickWord)
  if (route.query.questionId) {
    toOpenQuesId.value = route.query.questionId
  }
  curHeaderMode.value = Number(sessionStorage.getItem('mode'))
  emitter.on(Event.GENERATE, () => isQuesGenerating.value = true)

  const res: any = await getGenerateCurrent(uniqueCode)
  if (res.success) {
    if (res.data.status == 0) {
      isQuesGenerating.value = true
    } else if (res.data.status == 1) {
      isQuesGenerating.value = false
      quesGenerating.value = true
      taskId.value = res.data.taskId
    } else if (res.data.status == 2) {
      clearInterval(timer)
      isQuesGenerating.value = false
      quesGenerating.value = false
      errorMessage.value = true
    }
  }
})
onUnmounted(() => {
  emitter.off(Event.CLICK_WORD, handleClickWord)
  sessionStorage.removeItem('mode')
  emitter.off(Event.GENERATE)
  routerPushStore.setData(null)
})
const mark =
  `$$\begin{align}(a+b)^2 &= a^2 + 2ab + b^2 \\(a+b)(a-b) &= a^2 - b^2\end{align}$$
`
</script>
<template>
  <div class="wrapper">
    <!-- <div v-html="processAllLatexEquations(mark)"></div> -->
    <div class="header">
      <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader">
        <template v-slot:mode0> 习题内容 </template>
        <template v-slot:mode1> 问题列表 </template>
      </mode-type-switcher>
      <div class="btn-container">
        <div v-if="!isQuesGenerating">
          <span class="download" v-if="quesGenerating" @click="downloadGenerateQues">下载问题</span>
          <span style="color: red;" v-if="errorMessage">生成问题失败，请重试</span>
          <CmpButton type="primary" class="w130" @click="openGenerateDialog">生成问题</CmpButton>
        </div>
        <div v-else>
          <span style="vertical-align: bottom">正在处理中...</span>
          <div class="w131">生成问题</div>
        </div>
      </div>
    </div>
    <!--<div class="line"></div>-->
    <div class="info-container" v-show="curHeaderMode === 0">
      <ExerInfoBlock :exercise="exercise"></ExerInfoBlock>
    </div>
    <span class="table-container" v-show="curHeaderMode === 1">
      <questionListTableForExer :exercise-id="exerciseId"></questionListTableForExer>
    </span>
  </div>
  <!-- 其他组件 -->
  <!-- 问题抽屉 -->
  <question-drawer ref="questionDrawerRef" :ques-onwer="questionOnwer"></question-drawer>
  <answer-drawer ref="answerDrawerRef" :ques-onwer="questionOnwer"></answer-drawer>
  <!-- 划词浮窗 -->
  <mul-question-list ref="mulQuesionListRef"></mul-question-list>
  <QuestionDialog></QuestionDialog>
  <AnswerDialog></AnswerDialog>
  <GenerateDialog></GenerateDialog>
</template>
<style scoped>
.line {
  width: 1240px;
  height: 1px;
  background-color: #ebeef5;
  margin: 0 auto;
}

.wrapper {
  background-color: white;
  width: var(--width-fixed--project);
  padding: 10px 0;
  margin: 0 auto 0;
  display: flex;
  flex-direction: column;

  .header {
    width: 1240px;
    display: flex;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    margin-bottom: 25px;
  }

  .btn-container {
    position: absolute;
    right: 0;
    top: 10px;

    .download {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: bottom;
      margin-right: 5px;
    }

    .download:hover {
      font-weight: 600;
    }

    .w130 {
      width: 120px;
      height: 35px;
      font-size: 14px;
      border-radius: 4px;
    }

    .w131 {
      width: 120px;
      height: 35px;
      border-radius: 2px;
      font-weight: 400;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      cursor: not-allowed;
      background-color: #DCDFE6;
      border: 1px solid #DCDFE6;
      color: white;
    }
  }

  .info-container {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    height: calc(100% - 60px);
    /* 减去header和line的高度 */
    overflow: hidden;
  }

  .table-container {
    display: flex;
    width: 100%;
    justify-content: center;
    padding: 10px 20px;
  }
}
</style>
