<script setup lang="ts">
import { onMounted, onUnmounted, ref, nextTick } from 'vue';
import { useFloating, shift, flip, offset, autoUpdate, size } from '@floating-ui/vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';

const curQuesList = ref([]);
const reference = ref<HTMLElement | null>(null);
const floating = ref(null);
const open = ref(false);

// 使用更完整的浮动UI配置
const { floatingStyles } = useFloating(reference, floating, {
  open,
  placement: 'bottom-start', // 使用底部左对齐
  middleware: [
    offset({ mainAxis: 5, crossAxis: 0 }), // 设置主轴偏移为5px，确保有一定距离
    flip({
      fallbackPlacements: ['top-start'], // 如果底部空间不足，翻转到顶部并保持左对齐
      padding: 10 // 与视口边缘保持10px的距离
    }),
    shift({
      padding: 10, // 确保内容不会太靠近视口边缘
      crossAxis: true // 允许在交叉轴上移动
    }),
    size({
      apply({ availableWidth, elements }) {
        // 限制浮动元素的最大宽度为可用宽度
        Object.assign(elements.floating.style, {
          maxWidth: `${Math.min(400, availableWidth)}px`
        });
      },
      padding: 10 // 与视口边缘保持10px的距离
    })
  ],
  whileElementsMounted: autoUpdate // 自动更新位置
});

// 检查元素是否在视口内
const isInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

// 展示卡片
const showCard = (data: { questionList: any[]; element: HTMLElement }) => {
  const { questionList, element } = data;
  if (element instanceof HTMLElement) {
    // 更新问题列表数据
    curQuesList.value = questionList;

    // 先关闭浮动内容，确保重新定位
    open.value = false;

    // 设置参考元素
    reference.value = element;

    // 使用setTimeout确保DOM更新和滚动完成后再显示浮动内容
    setTimeout(() => {
      // 打开浮动内容
      open.value = true;

      // 在浮动内容渲染后，确保它在视口内
      nextTick(() => {
        if (floating.value) {
          const floatingElement = floating.value as HTMLElement;

          // 如果浮动元素不在视口内，调整其位置
          if (!isInViewport(floatingElement)) {
            floatingElement.scrollIntoView({
              behavior: 'smooth',
              block: 'nearest',
              inline: 'nearest'
            });
          }
        }
      });
    }, 100); // 短暂延迟确保滚动已完成
  } else {
    console.error('Passed element is not an instance of HTMLElement');
  }
};
// 处理选择问题
const handleSelectQues = (select: any) => {
  // 只发送事件打开抽屉，不自动关闭悬浮内容
  emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: select.questionId });
};
// 点击卡片外关闭卡片
const closeCard = () => {
  open.value = false;
};
// 点击事件处理函数 - 使用捕获阶段
const handleDocumentClick = () => {
  // 如果悬浮内容未打开，不需要处理
  if (!open.value) return;

  // 检查点击是否在浮动元素内
  closeCard();
};

// 监听抽屉打开事件，关闭悬浮内容
// const handleDrawerOpen = () => {
//   // 当抽屉打开时，直接关闭悬浮内容
//   if (open.value) {
//     // 使用setTimeout确保抽屉打开后再关闭悬浮内容
//     setTimeout(() => {
//       open.value = false;
//     }, 100);
//   }
// }

onMounted(() => {
  emitter.on(Event.SHOW_MUL_LIST, showCard);

  // 添加全局点击事件监听 - 使用捕获阶段
  document.addEventListener('click', handleDocumentClick, true);

  // 为抽屉添加点击事件监听
  const addDrawerClickListener = () => {
    const drawerElements = document.querySelectorAll('.el-drawer');
    drawerElements.forEach((drawer) => {
      drawer.addEventListener('click', () => {
        // 如果悬浮内容打开，点击抽屉内部时关闭悬浮内容
        if (open.value) {
          closeCard();
        }
      });
    });
  };

  // 使用MutationObserver监听DOM变化，当抽屉被添加到DOM时添加点击事件监听
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        // 检查是否有新的抽屉元素被添加
        setTimeout(addDrawerClickListener, 100);
      }
    });
  });

  // 开始观察document.body的子节点变化
  observer.observe(document.body, { childList: true, subtree: true });
});

onUnmounted(() => {
  emitter.off(Event.SHOW_MUL_LIST, showCard);

  // 移除事件监听器，注意第三个参数需要与添加时一致
  document.removeEventListener('click', handleDocumentClick, true);

  // 移除所有抽屉上的点击事件监听器
  const drawerElements = document.querySelectorAll('.el-drawer');
  drawerElements.forEach((drawer) => {
    // 使用克隆节点替换原节点，移除所有事件监听器
    const newDrawer = drawer.cloneNode(true);
    if (drawer.parentNode) {
      drawer.parentNode.replaceChild(newDrawer, drawer);
    }
  });
});
defineExpose({
  showCard
});
</script>
<template>
  <div
    ref="floating"
    :style="open ? floatingStyles : { position: 'fixed', top: '-9999px', left: '-9999px' }"
    class="floatingContainer"
    style="z-index: 99999"
  >
    <Transition name="scale">
      <div v-if="open && curQuesList && curQuesList.length > 0" class="floating-content">
        <div
          v-for="(question, index) in curQuesList"
          :key="index"
          class="floating-content-item"
          @click="handleSelectQues(question)"
        >
          <div style="display: flex; align-items: center">
            <span class="keyword-container">
              【

              <span
                class="ellipsis-text-inline keyWords"
                style="word-break: break-all"
                v-html="question.keyword"
              ></span>

              】
            </span>
            <span v-if="question.questionType != '开放性问题'">{{ question.questionType }}?</span>
            <span v-else v-html="question.questionDescription"></span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<style scoped>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.card-list {
  padding: 5px;
  width: auto;
  max-width: 1000px;
  background-color: var(--color-primary);
  color: white;
  font-family: var(--text-family);
  font-size: 14px;
}
:v-deep .el-card__body {
  padding: 5px;
}
.card-item {
  width: calc(100% - 20px); /* 根据需要调整卡片宽度 */
}

.card-content {
  display: block;
  white-space: nowrap;
  margin-bottom: 5px;
  &:hover {
    font-weight: bold;
    cursor: pointer;
  }
}

.question-type {
  margin-left: 10px;
  display: inline; /* 使问题类型也在同一行显示 */
}
.html-content {
  display: inline;
}
.keyword-container {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  max-width: 320px;
}
</style>
