/// <reference types="vite/client" />
interface ImportMetaEnv {
    readonly VITE_NODE_ENV: string;
    readonly VITE_APP_YOUTH_URL: string,
    readonly VITE_DUTCH_URL: string,
    readonly VITE_PARENT_HOST: string,
    readonly VITE_APP_YOUTH_URL: string,
    readonly VITE_DUTCH_URL: string,
    readonly VITE_COS_BUCKET_NAME: string,
    readonly VITE_COS_REGION: string,
}

interface ImportMeta {
    readonly env: ImportMetaEnv
}

declare module '*.vue' {
    import type { DefineComponent } from 'vue'
    const component: DefineComponent<{}, {}, any>
    export default component
}