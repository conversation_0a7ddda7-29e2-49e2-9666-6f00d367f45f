import { APIResponse } from "../../utils/type";
import { http } from "../index";

export interface params2TagPrjList{
    current:number,
    limit:number,
    tprjTitle?:string, 
    tprjForm:number, // 视频:1 文稿:2 全部:0
}

export interface params2BindTag {
    uniqueCode: string,
    tagCodeList: string[],
}

export interface params2tList {
    current: number;
    limit: number;
    keyword: string;
    status: number;
}

// 获取项目列表
export function getTagPrjListApi (params: params2TagPrjList) : Promise<APIResponse>{
    return http.post("/tagServer/getTagList",params)
}
// 绑定tag
export function bindTagListApi (params: params2BindTag) : Promise<APIResponse>{
    return http.post("/tagServer/bindTags", params)
}
// 获取tag列表
export function getTagListApi (params: params2tList) : Promise<APIResponse>{
    return http.post("/tagServer/getList", params)
}