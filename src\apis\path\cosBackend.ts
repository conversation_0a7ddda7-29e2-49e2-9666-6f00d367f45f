import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

// 获取预览视频的cos-key 这里其实是应该获取tmpkey但是忘记改了
export function getM3U8Key(): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: '/cos-sts-client/getTemporaryKey',
    });
}

// export function getTmpSecretKey(prjId: number): Promise<APIResponse> {
//     return http.request({
//         method: 'get',
//         url: `/welman/cos-sts-client/getTemporaryKey/${prjId}`,
//     });
// }

// export function getTmpSecretKey(): Promise<APIResponse> {
//     return http_prj.request({
//         method: 'get',
//         url: `/welman/cos-sts-client`,
//     });
// }
// export function uploadVideo2Backend(secId: number, videoKey: string): Promise<APIResponse> {
//     return http_prj.request({
//         method: 'post',
//         url: '/welman/video/updateVideo',
//         data: {
//             sectionId: secId,
//             url: videoKey,
//         }
//     });
// }
// export function getM3U8Key(mp4Key: string): Promise<APIResponse> {
//     return http.request({
//         method: 'post',
//         url: '/welman/video/getProcessedKey',
//         data: {
//             mp4Key: mp4Key,
//         }
//     });
// }
