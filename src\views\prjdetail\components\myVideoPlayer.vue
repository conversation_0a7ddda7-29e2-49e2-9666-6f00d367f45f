<script setup lang="ts">
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import {onMounted, ref, onBeforeUpdate, onBeforeUnmount, watch, watchEffect} from 'vue';

const player = ref();
const secId = ref();
const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  },
  sectionId: {
    type: Number,
    required: false,
    default: 0
  }
})
watchEffect(() => {
  secId.value = props.sectionId;
})
onMounted(() => {
  // setTimeout(() => {
    if (props.videoSrc != undefined) {
      player.value = videojs(`myVideo_${props.sectionId}`, {
        sources: props.videoSrc,
        controlBar: true,
        playbackRates: [0.5, 1, 1.5, 2, 3], //倍速播放配置
        fluid: true,
        type: 'application/x-mpegURL'
      });
      // console.log('debug: 加载正常');
    }
  // },1000)
});
// watch(()=>props.videoSrc, newVal => {
//   console.log('debug: newVideo in player: ', newVal)
//   if (!newVal) {
//     player.value.dispose();
//   }
//   else {
//     player.value = videojs(`myVideo_${secId.value}`, {
//       sources: newVal,
//       controlBar: true,
//       playbackRates: [0.5, 1, 1.5, 2, 3], //倍速播放配置
//       fluid: true,
//       type: 'application/x-mpegURL'
//     });
//   }
//   // player.value.load();
//   // player.value.play();
// },{deep: true, immediate: false})
onBeforeUnmount(() => {
  player.value.pause();
  player.value.dispose();
});
</script>

<template>
  <div class="video-wrapper">
    <!-- 此处修改过object-fit,若想要视频有不同的展现形式，可以调整一下这个参数 -->
    <video
        v-if="secId != undefined"
        :id="`myVideo_${secId}`"
        class="video video-js vjs-default-skin"
        muted
        controls
        preload="auto"
        style="width: 100%; height: 100%; object-fit: contain"
    ></video>
  </div>
</template>

<style scoped>
.video-wrapper {
  height: 100%;
  width: 100%;
  .video {
    height: 100%;
    background-color: white;
  }
}
.vjs-fluid:not(.vjs-audio-only-mode) {
  padding-top: 0;
}
:deep(.vjs-visible-text) {
  font-family: var(--font-family-text);
  cursor: pointer;
  /* margin: 10px 0 0 10px; */
  /* background-repeat: no-repeat; */
}
:deep(.vjs-control-bar) {
  height: auto;
}
:deep(.vjs-progress-control) {
  margin: auto;
}
:deep(.vjs-icon-placeholder::before) {
  position: static !important;
}
</style>
