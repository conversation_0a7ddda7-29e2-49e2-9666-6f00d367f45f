<script setup lang="ts">
import router from '@/router';
import { ref, watch } from 'vue';

const props = defineProps({
  action: {
    type: Number,
    required: false
  },
  form: {
    type: Number,
    required: true
  },
  isShowing: {
    type: Boolean,
    required: false,
    default: false
  }
});
const curForm = ref<string>();
const curAction = ref<number>();
const isStep = ref<boolean>();
watch(
  () => props,
  (newValue) => {
    console.log('watched in mystep: ' + JSON.stringify(newValue, null, 2));
    curForm.value = newValue.form.toString();
    curAction.value = newValue.action;
    isStep.value = newValue.isShowing;
  },
  { deep: true, immediate: true }
);
const toList = () => {
  router.push('/home')
}
</script>

<template>
  <div class="container">
    <div class="form" v-if="curForm == '1'">
      <img src="@/assets/images/prjForm/u3454.svg" />
      <div>视频项目</div>
    </div>
    <div class="form" v-else-if="curForm == '2'">
      <img src="@/assets/images/prjForm/u3462.svg" />
      <div>文稿项目</div>
    </div>
    <div class="back" @click="toList">返回列表</div>
  </div>
</template>

<style scoped lang="less">
.container {
  width: 100%;
  height: 56px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 30px;
  padding-right: 30px;
  border-bottom: solid 1px #dcdfe6;
  .form {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-primary);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    img {
      width: 18px;
      height: 16px;
      margin-right: 10px;
    }
  }
  .back {
    font-size: 14px;
    font-weight: 400;
    color: var(--color-primary);
    cursor: pointer;
    &:hover {
      font-weight: 800;
    }
  }
}
</style>
