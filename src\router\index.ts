import { userInfoStore } from "@/stores/userInfo";
import {
  createRouter,
  createWebHistory,
  type NavigationGuardNext,
  type RouteLocationNormalized,
} from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      redirect: "/home",
    },
    {
      path: "/",
      name: "",
      component: () => import("@/layout/Main.vue"),
      children: [
        {
          path: "/home",
          name: "home",
          component: () => import("@/views/myproject/index.vue"),
        },
        {
          path: "/question",
          name: "question",
          component: () => import("@/views/question/index.vue"),
        },
        {
          path: "/exercise",
          name: "exercise",
          component: () => import("@/views/myexercise/index.vue"),
        },
        {
          path: "/exerdetail",
          name: "exercisedetail",
          component: () => import("@/views/exercisedetail/index.vue"),
          beforeEnter: (_to, _from, next) => {
            if (_from.name === 'answer') {
              sessionStorage.setItem('mode', '1')
              next();
            } else {
              next()
            }
          }
        },
        {
          path: "/tag",
          name: "tag",
          component: () => import("@/views/tag/index.vue"),
        },
        {
          path: "/prjdetail",
          name: "prjdetail",
          component: () => import("@/views/prjdetail/index.vue"),
          beforeEnter: (_to, _from, next) => {
            if (_from.name === 'answer') {
              sessionStorage.setItem('mode', '1')
              next();
            } else {
              next()
            }
          }
        },
        {
          path: "/answer",
          name: "answer",
          component: () => import("@/views/answer/index.vue"),
        }
      ],
    },
  ],
});

router.beforeEach((_to, _from, next) => {
  const userinfo = userInfoStore();
  const userId = userinfo.getUserId();
  if (userId) {
    next();
  } else {
    userinfo
      .getUserInfo()
      .then(() => {
        next();
      })
      .catch((error) => console.error(error));
  }
});

export default router;
