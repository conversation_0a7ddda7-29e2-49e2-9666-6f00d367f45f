<script setup lang="ts">
// 组件
import FormSwitch from '@/components/FormSwitch.vue';
import MyFlipper from '@/components/MyFlipper.vue';

import { onMounted, onUnmounted, ref } from 'vue';
import { findKeyByValue } from '@/utils/func';
import { ExerciseType, ExerciseTypeWithAllDict } from '@/utils/constant';
import {
  getExerciseListApi,
  getExerDetailApi,
  type params2GetExerList
} from '@/apis/path/exercise';
import router from '@/router';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useExerciseWordStoreV2 } from '@/stores/exerciseWord';
import { useRouterPushStore } from '@/stores/routerPushStore';
import { Event } from '@/types/event';
import { emitter } from '@/utils/emitter';
import { renderMarkdown } from '@/utils/markdown';
import { processAllLatexEquations } from '@/utils/latexUtils';

const routerPushStore = useRouterPushStore();
const exerciseStore = useExerciseWordStoreV2();
const drawerControllerStore = useDrawerControllerStore();
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const params = ref({
  current: currentPage.value,
  limit: pageSize.value,
  type: '',
  stem: ''
});
// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
// 换页
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  params.value.current = newPage;
  getExerciseList();
};
// 获取习题列表
const getExerciseList = () => {
  const params2api = {
    current: currentPage.value,
    limit: params.value.limit,
    type: params.value.type === '0' ? '' : params.value.type,
    stem: params.value.stem
  };
  getExerciseListApi(params2api).then((res) => {
    if (res.success) {
      tableData.value = res.data.records;
      total.value = res.data.total;
    }
  });
};
// 处理刷新
const handleRefreshList = (form: any) => {
  params.value.type = form.type.toString();
  params.value.stem = form.stem;
  getExerciseList();
};
// 处理提问
const handleQues = (id: string) => {
  drawerControllerStore.setMode(false);
  exerciseStore.regString = '';
  window.open(`/exerdetail?uniqueCode=${id}`, '_blank');
};

onMounted(() => {
  getExerciseList();
  emitter.on(Event.REFRESH_LIST, getExerciseList);
});
onUnmounted(() => {
  emitter.off(Event.REFRESH_LIST, getExerciseList);
});
</script>

<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <form-switch
        :need-exer-type="true"
        :need-exer-search="true"
        @exercise="handleRefreshList"
      ></form-switch>
    </div>
    <div class="main-wrapper">
      <el-table
        class="table"
        ref="tableRef"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
        :row-style="{ height: '46px', overflow: 'hidden' }"
        :header-cell-style="{ 'font-weight': 600, 'font-size': '14px', color: '#333333' }"
      >
        <el-table-column type="index" :index="indexMethod" label="序号" width="60" align="center" />
        <el-table-column label="习题题干" header-align="center">
          <template #default="scope">
            <span style="white-space: nowrap" class="questionList" v-html="scope.row.stem"></span>
          </template>
        </el-table-column>
        <el-table-column label="习题类型" width="200" align="center">
          <template #default="scope">
            {{ findKeyByValue(scope.row.type, ExerciseTypeWithAllDict) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="操作时间" width="200" align="center" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <span class="operationBlock">
              <span @click="handleQues(scope.row.exerciseId)" class="operationBtn">提问</span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- other -->
</template>
<style scoped>
:deep(p) {
  display: inline;
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
.wrapper {
  background-color: white;
  width: var(--width-fixed--project);
  padding: 0 0 30px 0;
  margin: 0 auto 0;

  .header-wrapper {
    position: relative;
    width: 100%;
    .toolbar {
      margin: 7px 0;
      padding: 0 20px;
    }
  }
  .header-wrapper::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #ebeef5;
  }
  .main-wrapper {
    padding: 0 30px;
    margin-top: 30px;
    .table {
      border-top: 1px solid #ebeef5;
      .operationBlock {
        display: inline;
        justify-content: space-between;
        padding: 0 20px;
        align-items: center;
        flex-direction: row;
        color: var(--color-primary);
        .operationBtn {
          padding: 0 10px;
          &:hover {
            cursor: pointer;
            color: var(--color-primary);
            font-weight: 600;
            /* text-decoration: underline; */
          }
        }
        .disabledBtn {
          padding: 0 10px;
          color: var(--color-light);
          &:hover {
            cursor: no-drop;
          }
        }
      }
    }
  }
}
:deep(.el-button--primary.is-text.is-disabled) {
  color: var(--color-boxborder);
}
</style>
