<template>
  <div class="project">
    <div class="left">
      <div class="card">
        <div class="card-title-line" @click="handleFold(1)">
          <h3 class="card-title">形式</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[1] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[1]">
          <el-checkbox-group v-model="params.formType" :min="1">
            <div class="select">
              <span>视频 </span>
              <el-checkbox :value="1" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
            <div class="select">
              <span>文稿</span>
              <el-checkbox :value="2" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>
      <div class="card">
        <div class="card-title-line" @click="handleFold(3)">
          <h3 class="card-title">排序</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[3] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[3]">
          <div class="select">
            <span>时间倒序</span>
            <el-radio v-model="params.sort" :label="1" @change="getNewPrjList">
              <template></template>
            </el-radio>
          </div>
          <div class="select">
            <span>时间正序</span>
            <el-radio v-model="params.sort" :label="2" @change="getNewPrjList">
              <template></template>
            </el-radio>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-title-line" @click="handleFold(4)">
          <h3 class="card-title">类型</h3>
          <div class="card-icon">
            <el-icon class="fold">
              <ArrowDown :class="isMenuShow[4] ? 'rotate-icon' : 'origin-icon'" />
            </el-icon>
          </div>
        </div>
        <div class="card-select" v-show="isMenuShow[4]">
          <el-checkbox-group v-model="params.type" :min="1">
            <div class="select">
              <span>案例学习</span>
              <el-checkbox :value="2" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
            <div class="select">
              <span>知识讲解</span>
              <el-checkbox :value="1" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
            <!--<div class="select">
              <span>知识测评</span>
              <el-checkbox :value="3" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>-->
            <div class="select">
              <span>领域讲解</span>
              <el-checkbox :value="4" checked @change="getNewPrjList">
                <template></template>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div class="right">
      <!--TODO 这行先不显示 后面再说 -->
      <div class="search-result">
        <div>
          <!-- <span v-show="keyword == '' && searchStatus">您筛选的关键字 : "{{ keyword }}"，</span>
          已为您搜索到了 {{ total }} 个结果。 -->
        </div>
        <div class="search">
          <el-input
            v-model="keyword"
            placeholder="请输入项目名称"
            @keydown.enter="handleSearch"
            @blur="handleSearch"
          >
          <template #suffix>
            <el-icon class="close-icon" style="cursor: pointer; margin-right: 5px;" @click="handleDelete"
              ><Close
            /></el-icon>
            <el-icon class="search-icon" style="cursor: pointer" @click="handleSearch"
              ><Search
            /></el-icon>
          </template>
          </el-input>
        </div>
      </div>
      <el-skeleton style="width: 300px" :rows="5" :loading="loading" :throttle="100" animated>
        <template #default>
          <div class="list">
            <VideoCard
              v-for="(video, idx) in videoList"
              :key="'_' + idx"
              v-bind="video"
              :uniqueCode="video.uniqueCode"
              :createTime="video.createTime"
            ></VideoCard>
          </div>
        </template>
        <template #template>
          <div class="list">
            <div v-for="i in 6" :key="'_' + i">
              <el-skeleton-item variant="image" style="width: 300px; height: 225px" />
              <div>
                <el-skeleton-item variant="p" style="width: 50%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <el-skeleton-item variant="p" style="width: 50%" />
                <el-skeleton-item variant="p" style="width: 100%" />
                <div style="display: flex; align-items: center; justify-items: space-between">
                  <el-skeleton-item variant="text" style="margin-right: 16px" />
                  <el-skeleton-item variant="text" style="width: 30%" />
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>

      <div class="pagination-block">
        <span
          class="footerBtn"
          @click="getMoreFn"
          id="getMoreFn"
          v-if="total > params.current * params.limit"
        >
          <span class="myicon"></span>
          加载更多
        </span>
        <span v-else>暂无更多数据</span>
      </div>
      <div class="scrollerFooter"></div>
    </div>
  </div>
  <BackTop></BackTop>
</template>

<script setup lang="ts">
import { Search, Close } from '@element-plus/icons-vue';
import VideoCard from '@/components/VideoCard.vue';
import BackTop from '@/components/Backtop.vue';
import { type getPrjListParams, getPrjList } from '@/apis/path/myprj';
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const loading = ref(true); // 骨架屏
const keyword = ref('');
const videoList = ref([]); // 项目列表
const total = ref(0);
const searchStatus = ref(false);
const isMenuShow = ref([true, true, true, true, true]);
const noMore = ref(false)
let intersectionObserver
const params = ref<getPrjListParams>({
  // @ts-ignore
  formType: [1, 2], // video视频项目1，draft文稿项目2
  sort: 1, // descend降序1， ascend升序2
  // @ts-ignore
  type: [1, 2], // 1是知识讲解，2案例学习，3知识评测
  current: 1,
  limit: 6,
  tprjTitle: keyword.value
});

// 转换params
// @ts-ignore
const convertParamsFn = (data) => {
  const { formType, type, tprjTitle } = data;
  return {
    ...data,
    formType: formType.length === 2 ? 99 : Number(formType),
    type: type.join('@@'),
    tprjTitle: tprjTitle
  };
};

// 搜索
const handleSearch = () => {
  searchStatus.value = true;
  getNewPrjList();
};

const handleDelete = () =>{
  keyword.value = ''
}

// 重置状态
const resetParams = () => {
  params.value.current = 1;
  params.value.limit = 6;
  params.value.tprjTitle = keyword.value;
};

// 刷新列表
const getNewPrjList = async () => {
  resetParams();
  noMore.value = false
  loading.value = true;
  const data = convertParamsFn(params.value);
  const res = await getPrjList(data);
  loading.value = false;
  videoList.value = res.data.list;
  total.value = res.data.total;
};
// 加载更多
const getMoreFn = async () => {
  if(!noMore.value) {
    params.value.current += 1;
    const data = convertParamsFn(params.value);
    const res = await getPrjList(data);
    // @ts-ignore
    videoList.value.push(...res.data.list);
    if(res.data.list.length === 0) {
      noMore.value = true
    }
  }
};

const handleFold = (index: number) => {
  isMenuShow.value[index] = !isMenuShow.value[index];
};

// 开启滚动条观察
const startWatchScrollerFooter = (observer: any) => {
  // 注册观察者
  observer = new IntersectionObserver(function (entries) {
    // 如果不可见，就返回
    if (entries[0].intersectionRatio <= 0) return
    getMoreFn()
  })
  const foot = document.querySelector(".scrollerFooter")
  if (foot !== null) {
    observer.observe(foot)
  }
}
// 结束滚动条观察
const endWatchScrollerFooter = (observer: any) => {
  if (observer) {
    observer.disconnect()
  }
}

onMounted(async () => {
  // @ts-ignore
  keyword.value = route.query.keyword;
  await getNewPrjList();
    // 开始观察
  startWatchScrollerFooter(intersectionObserver)
})
// 断开所有观察
onUnmounted(() => {
  endWatchScrollerFooter(intersectionObserver)
})



watch(
  () => route.query.keyword,
  () => {
    // @ts-ignore
    keyword.value = route.query.keyword;
    getNewPrjList();
  }
);
</script>

<style scoped lang="less">
.el-checkbox {
  /* 重写element */
  // --el-color-primary: var(--color-primary);
  --el-color-primary: white;
  --el-color-white: var(--color-black);
  --el-checkbox-checked-input-border-color: var(--color-black);
}
.project {
  /* 重写element */
  background-color: white;
  padding: 20px 0 10px 0;
  width: var(--width-fixed--project);
  // padding: var(--padding-box);
  margin: 15px auto 0;
  display: flex;
  align-items: flex-start;
  //height: 98%;
  .rotate-icon {
    transition: transform 0.5s ease-in-out;
    transform: rotate(180deg);
  }
  .origin-icon {
    transition: transform 0.5s ease-in-out;
    transform: rotate(0deg);
  }
  .left {
    width: 290px;
    padding-right: 10px;
    padding-left: 20px;
    position: relative;

    // card 标题
    .card-title-line {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 5px;
      border-bottom: 1px solid rgb(121, 121, 121);

      .card-title {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        font-family: var(--title-family);
      }

      .card-icon {
        display: flex;
        align-items: center;

        .more {
          font-size: 12px;
          cursor: pointer;

          &:hover {
            font-weight: 600;
          }
        }

        .el-icon {
          margin-left: 5px;
        }
      }
    }

    .card-select {
      .select {
        font-size: 16px;
        font-family: var(--text-family);
        display: flex;
        align-items: center;
        justify-content: space-between;
        // margin-top: 15px;
      }
    }

    .card {
      margin-top: 40px;
    }

    .card:first-of-type {
      margin-top: 0px;
    }

    .theme {
      position: relative;
      margin-top: 0px;
    }

    .layer {
      position: absolute;
      background-color: rgba(51, 51, 51, 0.6);
      padding: 18px 0px 0px 18px;
      margin-right: 10px;
      width: calc(100% - 10px);
      height: calc(100% - 20px);
      transform: translateY(30px);
      //   opacity: 0;
      display: none;
      z-index: 10;

      .layer-line {
        display: flex;
        align-items: center;

        // line-height: 25px;
        // height: 25px;
        .layer-line-text {
          width: 228px;
          height: 25px;
          line-height: 25px;
          color: var(--color-black);
          background-color: white;
          padding-left: 10px;
          border-radius: 10px;
          margin-right: 10px;
          cursor: pointer;
          border: 1px solid transparent;
          display: flex;
          align-items: center;
          font-size: 14px;

          &:hover {
            color: var(--color-primary);
            background-color: var(--color-second);
          }
        }
      }
    }

    .layer.show {
      display: block;
      //overflow: auto;
      //   opacity: 1;
    }
  }

  .right {
    flex-grow: 1;
    margin-left: 20px;
    height: 100%;
    //overflow-y: auto;

    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
    .search-result {
      font-size: 14px;
      font-weight: 400;
      color: #333333;

      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .search {
        margin-right: 40px;
        .el-input {
          width: 225px;
        }
      }
    }

    .list {
      margin-top: 20px;
      display: grid;
      grid-template-columns: repeat(3, 300px); // 重复4次300px
      grid-column-gap: 25px; // 列之间的距离 34px
      grid-row-gap: 10px; // 行之间的距离 10px
    }

    .pagination-block {
      display: flex;
      text-align: center;
      justify-content: center;
      color: var(--color-primary);
      font-family: var(--text-family);
      font-weight: 400;
      font-size: 14px;
      height: 20px;
      line-height: 20px;
      margin: 10px 0;

      .footerBtn {
        width: 90px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .myicon {
          width: 12px;
          height: 12px;
          margin-right: 5px;
          background-image: url('@/assets/images/project/u3964.svg');
        }
        &:hover {
          font-weight: bold;
        }
      }
    }
  }
  .fold {
    cursor: pointer;
  }
}
</style>
