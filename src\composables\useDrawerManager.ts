import { ref, nextTick, type Ref } from 'vue';

/**
 * 抽屉管理选项
 */
interface DrawerManagerOptions {
  /** 问题抽屉关闭回调 */
  onQuestionDrawerClose?: () => void;
  /** 答案抽屉关闭回调 */
  onAnswerDrawerClose?: () => void;
  /** 初始z-index值 */
  initialZIndex?: number;
  /** 项目作者信息 */
  projectAuthor?: string;
}

/**
 * 抽屉管理返回值
 */
interface DrawerManagerReturn {
  /** 问题抽屉显示状态 */
  showQuestionDrawer: Ref<boolean>;
  /** 答案抽屉显示状态 */
  showAnswerDrawer: Ref<boolean>;
  /** 选中的文本 */
  selectedText: Ref<string>;
  /** 当前问题数据 */
  currentQuestionData: Ref<any>;
  /** 组件z-index管理 */
  componentZIndex: Ref<{ question: number; answer: number }>;
  /** 组件栈 */
  componentStack: Ref<('question' | 'answer')[]>;
  /** 项目作者 */
  projectAuthor: Ref<string>;
  /** 更新组件层级 */
  updateComponentLayer: (componentType: 'question' | 'answer') => void;
  /** 显示问题抽屉 */
  handleShowQuestionDrawer: (event: CustomEvent) => void;
  /** 显示答案抽屉 */
  handleShowAnswerDrawer: (event: CustomEvent) => void;
  /** 关闭问题抽屉 */
  handleCloseQuestionDrawer: () => void;
  /** 关闭答案抽屉 */
  handleCloseAnswerDrawer: () => void;
  /** 从浮动元素显示问题 */
  handleShowQuestionFromFloating: (question: any) => void;
  /** 初始化事件监听 */
  initializeEventListeners: () => void;
  /** 清理事件监听 */
  cleanupEventListeners: () => void;
}

/**
 * 抽屉组件管理 Composable
 * 用于管理问题抽屉和答案抽屉的显示、隐藏和层级管理
 */
export function useDrawerManager(options: DrawerManagerOptions = {}): DrawerManagerReturn {
  const {
    onQuestionDrawerClose,
    onAnswerDrawerClose,
    initialZIndex = 1001,
    projectAuthor: initialProjectAuthor = ''
  } = options;

  // 响应式状态
  const showQuestionDrawer = ref(false);
  const showAnswerDrawer = ref(false);
  const selectedText = ref('');
  const currentQuestionData = ref<any>(null);
  const componentStack = ref<('question' | 'answer')[]>([]);
  const componentZIndex = ref({
    question: initialZIndex,
    answer: initialZIndex
  });
  const projectAuthor = ref(initialProjectAuthor);

  /**
   * 更新组件层级
   * @param componentType 组件类型
   */
  const updateComponentLayer = (componentType: 'question' | 'answer') => {
    // 移除已存在的组件类型
    const index = componentStack.value.indexOf(componentType);
    if (index > -1) {
      componentStack.value.splice(index, 1);
    }

    // 将当前组件添加到栈顶
    componentStack.value.push(componentType);

    // 更新z-index，栈顶组件z-index最高
    componentStack.value.forEach((type, idx) => {
      componentZIndex.value[type] = initialZIndex + idx;
    });
  };

  /**
   * 显示问题抽屉
   * @param event 自定义事件
   */
  const handleShowQuestionDrawer = (event: CustomEvent) => {
    const { selectedText: text } = event.detail;
    selectedText.value = text;
    showQuestionDrawer.value = true;

    // 更新组件层级
    updateComponentLayer('question');
  };

  /**
   * 显示答案抽屉
   * @param event 自定义事件
   */
  const handleShowAnswerDrawer = (event: CustomEvent) => {
    const { questionData } = event.detail;

    // 强制触发props变化：先清空再设置，确保Vue能检测到变化
    currentQuestionData.value = null;

    // 使用nextTick确保Vue响应式系统处理了null值
    nextTick(() => {
      currentQuestionData.value = questionData;
      showAnswerDrawer.value = true;

      // 更新组件层级
      updateComponentLayer('answer');
    });
  };

  /**
   * 关闭问题抽屉
   */
  const handleCloseQuestionDrawer = () => {
    showQuestionDrawer.value = false;
    selectedText.value = '';

    // 从组件栈中移除
    const index = componentStack.value.indexOf('question');
    if (index > -1) {
      componentStack.value.splice(index, 1);
    }

    // 重新分配z-index
    componentStack.value.forEach((type, idx) => {
      componentZIndex.value[type] = initialZIndex + idx;
    });

    // 调用回调函数
    if (onQuestionDrawerClose) {
      onQuestionDrawerClose();
    }

    // 通知App.vue QuestionDrawer已关闭
    const event = new CustomEvent('questionDrawerClosed');
    window.dispatchEvent(event);
  };

  /**
   * 关闭答案抽屉
   */
  const handleCloseAnswerDrawer = () => {
    showAnswerDrawer.value = false;
    currentQuestionData.value = null;

    // 从组件栈中移除
    const index = componentStack.value.indexOf('answer');
    if (index > -1) {
      componentStack.value.splice(index, 1);
    }

    // 重新分配z-index
    componentStack.value.forEach((type, idx) => {
      componentZIndex.value[type] = initialZIndex + idx;
    });

    // 调用回调函数
    if (onAnswerDrawerClose) {
      onAnswerDrawerClose();
    }
  };

  /**
   * 从浮动元素显示问题
   * @param question 问题对象
   */
  const handleShowQuestionFromFloating = (question: any) => {
    // 设置当前问题数据为选中的问题（传递问题对象而不是HTML元素）
    currentQuestionData.value = question;

    // 确保 AnswerDrawer 显示
    showAnswerDrawer.value = true;

    // 更新组件层级
    updateComponentLayer('answer');
  };

  /**
   * 初始化事件监听
   */
  const initializeEventListeners = () => {
    // 添加QuestionDrawer事件监听
    window.addEventListener('showQuestionDrawer', handleShowQuestionDrawer as EventListener);
    
    // 添加AnswerDrawer事件监听
    window.addEventListener('showAnswerDrawer', handleShowAnswerDrawer as EventListener);
  };

  /**
   * 清理事件监听
   */
  const cleanupEventListeners = () => {
    // 移除QuestionDrawer事件监听
    window.removeEventListener('showQuestionDrawer', handleShowQuestionDrawer as EventListener);
    
    // 移除AnswerDrawer事件监听
    window.removeEventListener('showAnswerDrawer', handleShowAnswerDrawer as EventListener);
  };

  return {
    showQuestionDrawer,
    showAnswerDrawer,
    selectedText,
    currentQuestionData,
    componentZIndex,
    componentStack,
    projectAuthor,
    updateComponentLayer,
    handleShowQuestionDrawer,
    handleShowAnswerDrawer,
    handleCloseQuestionDrawer,
    handleCloseAnswerDrawer,
    handleShowQuestionFromFloating,
    initializeEventListeners,
    cleanupEventListeners
  };
}
