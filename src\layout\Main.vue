<template>
  <div class="app-container">
    <div class="header">
      <Header></Header>
    </div>
    <div class="content-container">
      <div class="left">
        <LeftMenu></LeftMenu>
      </div>
      <div class="main">
        <div class="mainWrapper">
          <RouterView></RouterView>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Header from "@/layout/Header.vue"
import LeftMenu from "@/layout/LeftMenu.vue"
</script>
<style scoped>
.app-container {
  font-family: var(--text-family);
  background-color: rgba(242, 242, 242, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  word-break: break-all;
  overflow: hidden;
  :deep(pre) {
    width: max-content;
    min-width: 100%;
  }

  .header {
    display: flex;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.996);
    box-shadow: rgba(149, 157, 165, 0.2) 0px 5px 24px;
    margin-bottom: 10px;
  }

  .content-container {
    height: calc(100% - 70px); /* 减去header高度(60px)和margin-bottom(10px) */
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .left {
      background-color: white;
      height: 100%;
      width: 200px;
    }
    .main {
      width: 100%;
      display: flex;
      justify-content: center;
      overflow-y: auto;
      .mainWrapper {
        /* margin-top: 20px; */
        margin-left: 20px;
        width: 100%;
        height: 100%;
        overflow-y: auto;
      }
      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 50px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
    }
  }
}
</style>
