<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { nextTick, onMounted, ref, watch } from 'vue';

const router = useRouter();
const route = useRoute();
import projectNegative from '../assets/left/project-negative.svg';
import projectActive from '../assets/left/project-active.svg';
import messageNegative from '../assets/left/message-negative.svg';
import messageActive from '../assets/left/message-active.svg';
import tagNegative from '../assets/left/tag-negative.svg';
import tagActive from '../assets/left/tag-active.svg';
import answerActive from '../assets/left/answer-active.svg';
import answerNegative from '../assets/left/answer-negative.svg'
import exerNegative from "@/assets/left/exer-negative.svg"
import exerActive from "@/assets/left/exer-active.svg"

const activeIndex = ref(-1);
const menuInfo = [
  {
    name: '我的项目',
    iconPath: [projectActive, projectNegative],
    toPath: '/home'
  },
  {
    name: '我的习题',
    iconPath: [exerActive, exerNegative],
    toPath: '/exercise'
  },
  {
    name: '问题消息',
    iconPath: [messageActive, messageNegative],
    toPath: '/question'
  },
  {
    name: '维护标签',
    iconPath: [tagActive, tagNegative],
    toPath: '/tag'
  },
  /*{
    name: '答案发布',
    iconPath: [answerActive, answerNegative],
    toPath: '/answer'
  }*/
  
];
// 处理改变机会目录项
const changeActive = (index: number) => {
  if (index == activeIndex.value) {
    return;
  }
  activeIndex.value = index;
  router.push(menuInfo[index].toPath);
};
// goToPage
watch(() => route, (newVal) => {
  activeIndex.value = menuInfo.findIndex(path => path.toPath === newVal.path);
}, { deep: true, immediate: true })
onMounted(() => {});
</script>

<template>
  <div class="leftMenu">
    <div class="menu">
      <div
        v-for="(menu, index) in menuInfo"
        class="menu-item"
        :class="activeIndex == index ? 'active' : ''"
        @click="changeActive(index)"
      >
        <img
          style="height: 16px; width: 16px; margin-right: 16px"
          :src="activeIndex === index ? menu.iconPath[0] : menu.iconPath[1]"
          alt=""
        />
        <!-- {{ menu }} -->
        {{ menu.name }}
      </div>
    </div>
    <div class="footer">© 无尽本源 版权所有</div>
  </div>
</template>

<style scoped lang="less">
.leftMenu {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 200px;
  background-color: white;
  /*position: relative;*/
  /*z-index: 3000;*/
  font-family: var(--font-family-text);

  .menu {
    width: 100%;
    display: flex;
    flex-direction: column;
    font-size: 16px;

    .menu-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 60px;

      &:hover {
        cursor: pointer;
        background-color: #fafafa;
      }
    }
  }

  .active {
    color: #1762ab;
  }
  .createBtn {
    margin: 15px 0;
  }

  .el-menu {
    border-right: none;
    padding-left: 10px;
    --el-menu-active-color: var(--color-primary);
    --el-menu-item-font-size: 16px;
    width: 100%;
    overflow-y: auto;
    height: calc(100% - 68px - 70px);

    .el-sub-menu.is-active {
      &:deep(.el-sub-menu__title) {
        color: var(--color-primary);
      }
    }

    &:deep(.el-menu-item-group__title) {
      padding: 0;
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    height: 68px;
    border-top: solid 2px #d8e1e9;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--color-grey);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 100%;
      height: 1px;
      margin: 0 20px;
      background-color: var(--color-line);
    }
  }
}

</style>
