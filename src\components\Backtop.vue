<template>
  <div :style="{ right: marginLeft }" class="backtopstyle" @click="gotoTop" v-if="backVisible">
    <span class="iconfont icon-xiangshang"></span>
  </div>
</template>
<script setup lang="ts">
import {ref, onMounted, onUnmounted} from 'vue'
// 回到顶部
const backVisible = ref(false);
const scrollTop = ref(0); // 距离顶部距离
let done = false; // 是否停止帧函数
// 检测距离
const handleScroll = () => {
  scrollTop.value =
    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
  scrollTop.value > 300 ? (backVisible.value = true) : (backVisible.value = false);
};
const step = () => {
  scrollTop.value <= 0 ? (done = true) : (done = false);
  window.scrollTo({
    top: 0
  });
  if (!done) {
    window.requestAnimationFrame(step);
  }
};
const gotoTop = () => {
  window.requestAnimationFrame(step);
};
// 设置监听与卸载
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
const calMarginRight = (mlp: number) => {
  const width = 1400;
  const result = (window.innerWidth - width) / 2 - mlp;
  if (result > 0) {
    return result + 'px';
  } else {
    return 0 + 'px';
  }
};

const marginLeft = ref('60px');
window.onresize = function () {
  marginLeft.value = calMarginRight(0);
};
// 挂载时
onMounted(() => {
  marginLeft.value = calMarginRight(0);
});
</script>

<style scoped lang="less">
.backtopstyle {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(220, 223, 230, 1);
  background-color: white;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  // font-size: 12px;
  color: rgba(220, 223, 230, 1);
  position: fixed;
  bottom: 210px; // (footer.height : 200px) + (margin-bottom : 10px)
  // right: 10%;
  z-index: 1;
}
.backtopstyle:hover {
  background-color: var(--color-primary);
  color: white;
  font-weight: bolder;
}
.icon-xiangshang {
  font-size: 16px;
}
</style>
