/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);

  /* --color-primary: #005579;*/
  --color-black: #333333;
  --color-deep: #666666;
  --color-grey: #999999;
  /* --color-primary: #1973cb; 主题颜色 */
  --color-second: #d6e9f6;
  --color-pay: #ffd37a;
  --color-inactive-project: #d7d7d7;
  --width-fixed--project: 1300px; /* 很多地方宽度定为1400px，左右padding设置为10px */
  --padding-box: 10px;
  --fontsize-small-project: 12px;
  --fontsize-middle-project: 14px; /*字号最小12，正文14，标题16向上每两个px一个层级 */
  --fontsize-large-project: 16px;
  /* --title-family: '黑体', sans-serif; */
  /* --text-family: '华文细黑', 'STXihei', sans-serif; */
  --title-family: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  --text-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;

  --color-primary: #1661AB;   /* 主题色 */
  --color-primary-transparent: rgba(22, 97, 171, 0.5);
  --color-second: #C5D5EA;    /* 辅助色 -hover */
  --color-black: #333333;     /* 主题字色 */
  --color-deep: #666666;      /* 导航次要字色 */
  --color-grey: #999999;      /* 次要字色 */
  --color-light: #C0C4CC;     /* 页面底色、失效字色 */
  --color-boxborder: #DCDFE6FE;
  --color-group-background: #DCDFE6; /* 页面次要元素 */
  --color-invalid: #C0C4CC;   /* 提示字色 */
  --color-line: rgb(216, 225, 233);
  --color-nosrc: #CCCCCC;     /* 阴影色 */
  --color-case: #FF9A6A;
  --color-explain: #ECC32F;
  --color-exam: #53C99F;

  --color-completed: rgba(103, 194, 58, 1);
  --color-returned: rgba(230, 162, 60, 1);
  --color-assigned: rgba(153, 153, 153, 1);
  --color-primary-rgba: rgba(22, 97, 171, 0.15);
  --color-completed-rgba: rgba(103, 194, 58, 0.15);
  --color-returned-rgba: rgba(230, 162, 60, 0.15);
  --color-assigned-rgba: rgba(153, 153, 153, 0.15);

  --color-release-status: #F56C6C;
  --color-release-status-rgba: rgb(245, 108, 108, 0.15);


  --fontsize-large-header: 28px;
  --fontsize-primary-header: 18px;
  --fontsize-small-header: 12px;
  --fontsize-mini-header: 10px;

  --color-back-header: #D6E9F6;
  --shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
  --el-color-primary: var(--color-primary);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;

}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 禁用body滚动，改为内部容器滚动 */
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 11px; /* 滚动条宽度 */
  height: 10px; /* 滚动条高度 */
}

::-webkit-scrollbar-thumb {
  background-color: #c1c1c1; /* 滚动条滑块颜色 */
  border-radius: 6px; /* 滑块圆角 */
}

::-webkit-scrollbar-track {
  background-color: #f0f0f0; /* 滚动条轨道颜色 */
}

