import { buildRegSubString, findAllOccurrences } from "./regUtils";
import { QuestionAction, type RenderInfo } from "@/types/word";

export function handleDraftQuestion(
  question: any,
  regString: string,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[],
  action: QuestionAction = QuestionAction.add
) {
  const regSubString = buildRegSubString(question.associatedWords);
  // console.log('regSubString', regSubString);
  if (regSubString.length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, regSubString);
  for (const position of positions) {
    handle(
      position,
      regSubString.length,
      question.questionId,
      renderInfoIndexes,
      renderInfoList,
      action
    );
  }
}

/**
 * 处理单个问题的标记信息
 * @param position - 问题在文本中的起始位置
 * @param length - 问题文本的长度
 * @param qid - 问题ID
 * @param renderInfoIndexes - 渲染信息索引数组
 * @param renderInfoList - 渲染信息列表
 * @param action - 操作类型(添加/删除)
 */
function handle(
  position: number,
  length: number,
  qid: number,
  renderInfoIndexes: number[],
  renderInfoList: RenderInfo[],
  action: QuestionAction
) {
  // 遍历问题文本的每个字符位置
  for (let i = position; i < position + length; i++) {
    // 获取当前位置对应的渲染信息索引
    const index = renderInfoIndexes[i];
    // 获取对应的渲染信息对象
    const renderInfo = renderInfoList[index];

    if (action == QuestionAction.add) {
      // 添加问题时,初始化计数为0并递增
      if (!renderInfo.questionCountMap[qid]) {
        renderInfo.questionCountMap[qid] = 0;
      }
      renderInfo.questionCountMap[qid]++;
    } else {
      // 删除问题时,直接删除该问题ID的计数
      delete renderInfo.questionCountMap[qid];
      // renderInfo.questionCountMap[qid]--; // 递减计数的替代方案
    }
  }
}

export function handleVideoQuestion(
  question: any,
  regString: string,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][],
  action: QuestionAction = QuestionAction.add
) {
  const regSubString = buildRegSubString(question.associatedWords);
  if (regSubString.length == 0) {
    return;
  }
  const positions = findAllOccurrences(regString, regSubString);
  for (const position of positions) {
    handle2(
      position,
      regSubString.length,
      question.questionId,
      renderInfoIndexes,
      renderInfoListList,
      action
    );
  }
}

function handle2(
  position: number,
  length: number,
  qid: number,
  renderInfoIndexes: Array<{
    listIndex: number;
    index: number;
  }>,
  renderInfoListList: RenderInfo[][],
  action: QuestionAction
) {
  for (let i = position; i < position + length; i++) {
    const { listIndex, index } = renderInfoIndexes[i];
    const renderInfo = renderInfoListList[listIndex][index];
    if (action == QuestionAction.add) {
      if (!renderInfo.questionCountMap[qid]) {
        renderInfo.questionCountMap[qid] = 0;
      }
      renderInfo.questionCountMap[qid]++;
    } else {
      delete renderInfo.questionCountMap[qid];
      // renderInfo.questionCountMap[qid]--;
    }
  }
}
