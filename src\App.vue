<script setup lang="ts">
import PicIcon from './assets/images/pic_icon.jpg';
import TableIcon from './assets/images/table_icon.jpg';
import CodeIcon from './assets/images/common/terminal.png';
import ThumbNail from './components/ThumbNail.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, onUnmounted, onUpdated, provide, ref, watch } from 'vue';
import { RouterView, useRouter } from 'vue-router';
import { getExerDetailApi } from '@/apis/path/exercise';
import { emitter } from './utils/emitter';
import { getProjectDetailApi } from '@/apis/path/prjdetail';
import { Event } from './types/event';
import { useRouterPushStore } from './stores/routerPushStore';
import { userInfoStore } from '@/stores/userInfo';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
import katex from 'katex';
import { bindKeyWordsHover } from '@/composables/useKeyWordsHover';
import { decodeHTML } from 'entities';
const routerPushStore = useRouterPushStore();

const thumbNailElement = ref<HTMLElement | null>(null);
// 窗口多开处理逻辑
const router = useRouter();
const thumbnail = ref();
const userinfo = userInfoStore();

document.addEventListener('visibilitychange', () => {
  if (!document['hidden']) {
    userinfo.getUserInfo();
    // console.log('出现');
  } else {
    //隐藏
    // console.log('隐藏');
  }
});
const imgs: Array<HTMLElement> = [];
const observer = new MutationObserver(() => {
  let scripts = Array.prototype.slice.call(document.body.getElementsByTagName('script'));
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }

    // 检查是否在 id=underline 元素内(可划词内容由划词包内容渲染），如果是则跳过渲染
    // let parentElement = script.parentElement;
    // while (parentElement) {
    //   if (parentElement.id && parentElement.id == 'underline') {
    //     return -1; // 跳过渲染
    //   }
    //   parentElement = parentElement.parentElement;
    // }

    // const stringIndex = script.getAttribute('stringIndex');
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? 'div' : 'span');
    katexElement.setAttribute('class', display ? 'equation' : 'inline-equation');

    // 使用安全的entities库进行HTML实体解码,避免xss攻击
    const decodedText = decodeHTML(script.text);
    katexElement.setAttribute('latexCode', script.text);

    try {
      // 预处理公式文本，将 align 环境替换为 align* 以禁用自动编号
      let processedText = decodedText.replace(/\s+/g, ' ');
      processedText = processedText.replace(/\\begin\{align\}/g, '\\begin{align*}');
      processedText = processedText.replace(/\\end\{align\}/g, '\\end{align*}');

      const htmlString = katex.renderToString(processedText, {
        displayMode: display,
        throwOnError: false,
        output: 'html'
      });

      katexElement.innerHTML = htmlString;
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = decodedText;
    }
    script.parentNode.replaceChild(katexElement, script);
  });

  const images = document.querySelectorAll('img');

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      'dblclick',
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
  });
  const questionLists = document.querySelectorAll('.questionList, .questionPop, .quesCollapse');
  questionLists.forEach((questionList) => {
    const imgElements = questionList.querySelectorAll('img:not([thumbnail])');
    imgElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = PicIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
    const tableElements = questionList.querySelectorAll('table:not([thumbnail])');
    tableElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = TableIcon;
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
  });

  // 处理 keyWords 区域的悬浮放大功能
  const keyWordsContainers = document.querySelectorAll('.keyWords');
  keyWordsContainers.forEach((container) => {
    const elements = container.querySelectorAll('.equation, img');
    elements.forEach((element) => {
      bindKeyWordsHover(element as HTMLElement);
    });
  });
});

// watch(
//   () => lineWordContent.value,
//   (newVal) => {
//     if (newVal) {
//       isQuestionDrawerOpen.value = true;
//     } else {
//       // 当lineWordContent被清空时，可能是弹窗关闭了
//       // 立即重置状态，不使用延迟
//       isQuestionDrawerOpen.value = false;
//     }
//   }
// );

router.beforeEach((to, from, next) => {
  // 检查是否跳转到 /exerdetail
  const uniqueCode = to.query.uniqueCode as string;
  if (to.path === '/exerdetail') {
    getExerDetailApi(uniqueCode).then((res) => {
      if (res.success) {
        routerPushStore.setData(res.data);
        next();
      } else {
        next(false);
        ElMessage.warning(res.message);
        if (from.path === '/exercise' || from.path === '/question') {
          emitter.emit(Event.REFRESH_LIST, true);
        }
      }
    });
  } else if (to.path === '/prjdetail') {
    getProjectDetailApi(uniqueCode).then((res) => {
      if (res.success) {
        routerPushStore.setData(res.data.list[0]);
        next();
      } else {
        next(false);
        ElMessage.warning(res.message);
        if (from.path === '/question') {
          emitter.emit(Event.REFRESH_LIST, true);
        }
      }
    });
  } else {
    next();
  }
});

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // // 监听抽屉关闭事件，重置lineWordContent
  // emitter.on('clear_line_word_content', () => {
  //   lineWordContent.value = '';
  //   isQuestionDrawerOpen.value = false;
  // });
});

onUnmounted(() => {
  observer.disconnect();
  // @ts-ignore
  // 移除事件监听器
  emitter.off('clear_line_word_content');
});
</script>

<template>
  <router-view></router-view>
  <ThumbNail ref="thumbnail" v-model="thumbNailElement"></ThumbNail>
</template>

<style scoped lang="less">
:deep(.el-drawer__header) {
  margin-bottom: 0;
}

:deep(.ck.ck-balloon-panel.ck-powered-by-balloon) {
  display: none;
}
</style>

<style>
.tooltip-width {
  max-width: 500px;
}

.el-drawer__header {
  padding: 20px 10px 0 !important;
}

.el-drawer__body {
  padding: 20px 10px 0 !important;
}
</style>
