<script setup lang="ts">
import { computed, inject, onMounted, ref } from "vue"
import MyUploadAdapter from "@/utils/ckEditorImgUploader"
// import CKEditor from "@ckeditor/ckeditor5-vue"
// import Editor from "ckeditor5-custom-build"
// const editor = Editor.ClassicEditorCustom
import { defineModel, defineProps } from "vue"
const Editor = inject("Editor") as any
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  height: {
    type: Number,
    default: 300,
  },
})
const editorData = ref()
const editorConfig = {
  placeholder: "请输入",
  extraPlugins: [MyCustomUploadAdapterPlugin],
  height: props.height,
}
const disable = ref()
const getData = () => {
  return editorData.value
}

const setData = (value: string, dis?: boolean) => {
  editorData.value = value
  disable.value = dis
  if (dis == undefined) {
    disable.value = false
  }
}
const model = defineModel()
function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader)
  }
}

onMounted(() => {
  model.value += " "
  document.documentElement.style.setProperty('--ck-height', `${props.height}px`);
})
defineExpose({
  getData,
  setData,
})
</script>

<template>
  <ckeditor
    class="classicEditor"
    :disabled="props.disabled"
    style="border: 1px solid #ccced1; border-radius: 5px"
    :editor="Editor.ClassicEditorCustom"
    v-model="model as string"
    :config="editorConfig"
  >
  </ckeditor>
</template>

<style scoped>
.ck.ck-editor__editable_inline > :last-child {
  margin-bottom: 0px;
}

.ck.ck-editor__editable_inline > :first-child {
  margin-top: 0px;
}
</style>
<style>
.ck.ck-editor__main > .ck-editor__editable {
  background: var(--ck-color-base-background);
  min-height: var(--ck-height);
  max-height: 500px;
}
</style>