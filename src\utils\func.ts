import { prjForm, prjType, typeDict_noDefault } from "@/utils/constant"
import type { prjInfoType, tagType } from "@/utils/type"
// @ts-ignore
import picSrc from "@/assets/images/common/pic_icon.jpg"
// @ts-ignore
import tableSrc from "@/assets/images/common/table.png"
// @ts-ignore
import codeSrc from "@/assets/images/common/terminal.png"
export const findKeyByValue = (
  value: string | number,
  dict: { [key: string]: string } | { [key: string]: number }
) => Object.keys(dict).find((key) => dict[key] === value)
export const decodeData = (data: any) => {
  // console.log("===>" + JSON.stringify(data))
  if (data.prform == null) data.prform = "暂无数据"
  else
    switch (data.prform.toString()) {
      case prjForm.video:
        data.prform = "视频"
        break
      case prjForm.text:
        data.prform = "文稿"
        break
      default:
        data.prform = "暂无数据"
        break
    }
  if (data.prtype == null) data.prtype = "暂无数据"
  else
    switch (data.prtype.toString()) {
      case prjType.klg:
        data.prtype = "知识讲解"
        break
      case prjType.prj:
        data.prtype = "案例学习"
        break
      case prjType.exam:
        data.prtype = "测评学习"
        break
      default:
        data.prtype = "暂无数据"
        break
    }
  // if (data.approvalAuthority == null) data.approvalAuthority = '暂无数据';
  // else switch (data.approvalAuthority.toString()) {
  //     case 1 :
  //         data.approvalAuthority = '';
  //         break;
  //     case 2 :
  //         data.approvalAuthority = '';
  //         break;
  //     case 3 :
  //         data.approvalAuthority = '';
  //         break;
  //     default: data.prtype = '暂无数据';
  //         break;
  // }
  // switch (data.condition.toString()) {
  //   case prjState.draft:
  //     data.condition = "草稿";
  //     break;
  //   case prjState.wait2examine:
  //     data.condition = "待审核";
  //     break;
  //   case prjState.examining:
  //     data.condition = "审核中";
  //     break;
  //   case prjState.back:
  //     data.condition = "已退回";
  //     break;
  //   case prjState.withdraw:
  //     data.condition = "已撤回";
  //     break;
  //   case prjState.publish:
  //     data.condition = "已发布";
  //     break;
  //   case prjState.delete:
  //     data.condition = "已删除";
  //     break;
  //   default: data.condition = '解析失败';
  //     break;
  // }
  return data
}
export const decodeData2 = (data: any) => {
  for (const key in data) {
    data[key] = data[key] !== null || undefined ? data[key] : "暂无数据"
  }
  return data
}
export const formatData_step1 = (data: any) => {
  // console.log("-->start format data in step1")
  const formatData: prjInfoType = {
    prjType: "",
    prjName: "",
    prjAim: "",
    prjGeneral: "",
    prjCover: {
      echoUrl: "",
      commUrl: "",
    },
    prjTagList: [],
    prjTargetList: [],
  }

  formatData.prjType = findKeyByValue(
    data.prjType.toString(),
    typeDict_noDefault
  ) as string
  formatData.prjName = data.title
  formatData.prjAim = data.purpose
  formatData.prjGeneral = data.description
  formatData.prjCover = {
    echoUrl: data.longUrl,
    commUrl: data.shortUrl,
  }
  if (data.prjTags) {
    formatData.prjTagList = data.prjTags.map(
      (t: { areaCode: number; title: string }) => {
        const ret: tagType = {
          areaCode: t.areaCode.toString(),
          title: t.title,
          choose: true,
        }
        return ret
      }
    )
  }
  if (data.targetKlgs) {
    formatData.prjTargetList = data.targetKlgs.map(
      (t: { klgCode: string; title: string }) => {
        const ret: tagType = {
          areaCode: t.klgCode,
          title: t.title,
          choose: true,
        }
        return ret
      }
    )
  }
  // console.log(
  //   "-->format data in step1 done: format type, coverUrl, tags, targets"
  // )
  return formatData
}
export const checkCoverUrl = (value: any, callback: any) => {
  if (!value || !value.commUrl) {
    return callback(new Error("请上传封面"))
  }
  callback()
}
export const checkVideoUrl = (value: any, callback: any) => {
  if (!value || !value.commUrl) {
    return callback(new Error("请上传视频"))
  }
  callback()
}
export const docCookies = {
  getItem: function (sKey: string): string | null {
    return (
      decodeURIComponent(
        document.cookie.replace(
          new RegExp(
            "(?:(?:^|.*;)\\s*" +
              encodeURIComponent(sKey).replace(/[-.+*]/g, "\\{{input}}") +
              "\\s*\\=\\s*([^;]*).*$)|^.*$"
          ),
          "$1"
        )
      ) || null
    )
  },
  setItem: function (
    sKey: string,
    sValue: string,
    sDomain: string,
    sPath?: string,
    vEnd?: number | string | Date,
    bSecure?: boolean
  ): boolean {
    if (!sKey || /^(?:expires|max-age|path|domain|secure)$/i.test(sKey)) {
      return false
    }
    let sExpires = ""
    if (vEnd) {
      switch (typeof vEnd) {
        case "number":
          sExpires =
            vEnd === Infinity
              ? "; expires=Fri, 31 Dec 9999 23:59:59 GMT"
              : "; max-age=" + vEnd
          break
        case "string":
          sExpires = "; expires=" + vEnd
          break
        case "object":
          sExpires = "; expires=" + (vEnd as Date).toUTCString()
          break
      }
    }
    document.cookie =
      encodeURIComponent(sKey) +
      "=" +
      encodeURIComponent(sValue) +
      sExpires +
      (sDomain ? "; domain=" + sDomain : "") +
      (sPath ? "; path=" + sPath : "") +
      (bSecure ? "; secure" : "")
    return true
  },
  removeItem: function (sKey: string, sPath: string, sDomain: string): boolean {
    if (!sKey || !this.hasItem(sKey)) {
      return false
    }
    document.cookie =
      encodeURIComponent(sKey) +
      "=; expires=Thu, 01 Jan 1970 00:00:00 GMT" +
      (sDomain ? "; domain=" + sDomain : "") +
      (sPath ? "; path=" + sPath : "")
    return true
  },
  hasItem: function (sKey: string): boolean {
    return new RegExp(
      "(?:^|;\\s*)" +
        encodeURIComponent(sKey).replace(/[-.+*]/g, "\\{{input}}") +
        "\\s*\\="
    ).test(document.cookie)
  },
  keys: /* optional method: you can safely remove it! */ function (): string[] {
    const aKeys = document.cookie
      .replace(/((?:^|\s*;)[^=]+)(?=;|$)|^\s*|\s*(?:=[^;]*)?(?:1|$)/g, "")
      .split(/\s*(?:=[^;]*)?;\s*/)
    for (let nIdx = 0; nIdx < aKeys.length; nIdx++) {
      aKeys[nIdx] = decodeURIComponent(aKeys[nIdx])
    }
    return aKeys
  },
}

export const transToIcon = (str: string) => {
  let updatedStr = str

  // 正则表达式匹配包含 img 标签的 figure
  const figureWithImgTagRegex = /<figure[^>]*>\s*<img[^>]*>[\s\S]*?<\/figure>/gi
  // 正则表达式匹配包含 table 标签的 figure
  const figureWithTableTagRegex =
    /<figure[^>]*>\s*<table[^>]*>[\s\S]*?<\/table>[\s\S]*?<\/figure>/gi
  // 正则表达式匹配不包含 img 或 table 的 figure
  const figureTagRegex = /<figure[^>]*>([\s\S]*?)<\/figure>/gi

  const imgTagRegex = /<img[^>]*>/gi // 正则表达式匹配所有 img 标签
  updatedStr = updatedStr.replace(
    imgTagRegex,
    `<img width="16" height="16" src="${picSrc}" />`
  )

  // 替换代码块（不包含在 figure 中的代码块）
  const code = `<img width="16" height="16" src="${codeSrc}" />`
  const codeBlockRegex = /<pre>\s*<code[^>]*>([\s\S]*?)<\/code>\s*<\/pre>/gi
  updatedStr = updatedStr.replace(codeBlockRegex, code)

  // 替换包含 img 标签的 figure
  updatedStr = updatedStr.replace(
    figureWithImgTagRegex,
    `<img width="16" height="16" src="${picSrc}" />`
  )
  // 替换包含 table 标签的 figure
  updatedStr = updatedStr.replace(
    figureWithTableTagRegex,
    `<img width="16" height="16" src="${tableSrc}" />`
  )

  return updatedStr
}
