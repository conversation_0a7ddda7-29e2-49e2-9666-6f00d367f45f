<script setup lang="ts">
import ModeTypeSwitcher from './components/modeTypeSwitcher.vue';
import QuestionListTable from './components/questionListTable.vue';
import MulQuestionList from './components/MulQuestionList.vue';
import { computed, inject, onMounted, onUnmounted, ref, nextTick, watch } from 'vue';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { findKeyByValue } from '@/utils/func';
import { getAllQuestionApi } from '@/apis/path/lineWordApis';
import {
  downloadGenerateQuestion,
  getGenerateCurrent,
  getSectionDetailApi
} from '@/apis/path/prjdetail';
import type { params2GetSecDetail } from '@/apis/path/prjdetail';
import { useRoute, useRouter } from 'vue-router';
import { ElLoading } from 'element-plus';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { storeToRefs } from 'pinia';
import { processAllLatexEquations } from '@/utils/latexUtils';
import AnswerDialog from '@/components/AnswerDialog.vue';
import QuestionDialog from '@/components/QuestionDialog.vue';
import { useRenderManager } from '@/composables/useRenderManager';
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import questionIcon from '@/assets/svgs/question.svg';
import { getQuestionList } from '@/utils/lineWord2V2';
import { useDialogStore } from '@/stores/dialog';

const drawerControllerStore = useDrawerControllerStore();
const dialogStore = useDialogStore();
const isQuesGenerating = ref(false);

const { mode } = storeToRefs(drawerControllerStore);
const handleWord = inject('handleWord') as (e: MouseEvent) => void;
const props = defineProps({
  curProjectId: {
    type: String,
    required: true
  },
  curSectionId: {
    type: Number,
    required: true
  }
});
const curPrjId = ref();
const curSecId = ref();
const curCntId = ref();
const getDataFlag = computed(() => {
  return curPrjId.value !== -1 && curSecId.value !== -1 && curPrjId.value && curSecId.value;
});
const curHeaderMode = ref<number>(0); // 0：项目内容 || 1：问题列表
const wordValue = ref<boolean>(false); // el-switch的value => false: 0 || true: 1
const route = useRoute();
const openQId = ref();
const questionList = ref<
  Array<{
    questionId: string;
    associatedWords: string;
  }>
>([]);
const content = inject('lineWordContent');
const wordContent = ref('');
const mulQuesionListRef = ref();

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon({
  onIconClick: (selectedText: string) => {
    // 使用 emitter 派发事件，与项目中的事件监听保持一致
    emitter.emit(Event.SHOW_QUESTION_DRAWER, {
      mode: 0,
      item: {
        content: selectedText,
        chapterId: curSecId.value,
        contentId: curCntId.value
      }
    });
  }
});

// 使用Render管理器
const renderContent = ref();
const { destroyRenderInstance, initializeRender, addQuestion, removeQuestion } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => wordContent.value,
  questionList,
  onClick: async (data: any) => {
    handleClickWord(data.target);
  },
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    } else {
      console.log('❌ 选中文本为空或无效');
    }
  },

  onFinish: (arg: any) => {
    const content = arg.content;
    renderContent.value = content;
  }
  // enableDebugLog: true
});

// watch(
//   () => content,
//   (newVal) => {
//     // @ts-ignore
//     if (newVal.value) {
//       const query = {
//         // @ts-ignore
//         content: newVal.value,
//         chapterId: curSecId.value,
//         contentId: curCntId.value
//       };
//       emitter.emit(Event.SHOW_QUESTION_DRAWER, { mode: 0, item: query });
//     }
//   },
//   { deep: true, immediate: true }
// );

// 处理转换header的mode
const handleChangeHeader = async (newHeaderMode: number) => {
  // 如果模式没有变化，不做任何处理
  if (curHeaderMode.value === newHeaderMode) return;

  // 更新模式
  curHeaderMode.value = newHeaderMode;
  wordValue.value = false;

  if (newHeaderMode === 0) {
    // 切换到项目内容
    drawerControllerStore.setMode(false);
  }
};

// 获取小节内容
const getSectionData = async () => {
  const params = ref<params2GetSecDetail>({
    sectionId: curSecId.value,
    uniqueCode: curPrjId.value
  });
  const res = await getSectionDetailApi(params.value);
  // @ts-ignore
  if (res.success) {
    questionList.value = [];
    wordContent.value = res.data.list.prText;
    curCntId.value = res.data.list.contentId;
    // 等待问题列表加载完成
    await handleQuestionList(curPrjId.value, curSecId.value);
  }
};

// 获取全部问题
const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getAllQuestionApi(chapterId, uniqueCode);
  if (!res.data) {
    console.warn('No question data received');
    return;
  }

  questionList.value = res.data;
  console.log('questionList.value', questionList.value);
};

// 添加问题函数
const addQuestionFn = (data: any) => {
  if (data.questionId && data.associatedWords) {
    // 使用 useRenderManager 的 addQuestion 方法
    addQuestion(data.associatedWords, Number(data.questionId));

    // 更新questionList
    questionList.value.push({
      questionId: data.questionId,
      associatedWords: data.associatedWords
    });
  }
};

// 删除问题函数
const removeQuestionFn = async (questionId: string) => {
  // 找到要删除的问题
  const questionIndex = questionList.value.findIndex((item) => item.questionId === questionId);
  if (questionIndex !== -1) {
    const question = questionList.value[questionIndex];

    // 使用 useRenderManager 的 removeQuestion 方法
    removeQuestion(question.associatedWords, Number(questionId));

    // 更新questionList
    questionList.value.splice(questionIndex, 1);
  }
};

// 自定义处理点击问题的函数，覆盖默认行为
const handleClickWord = async (el: HTMLElement) => {
  if (!el) {
    return;
  }
  // 获取问题列表
  const id = el.getAttribute('data-qid');

  const questionList = await getQuestionList(id);

  if (questionList.length > 0) {
    // 显示浮窗，无论有几个问题
    if (mulQuesionListRef.value && mulQuesionListRef.value.showCard) {
      // 直接调用组件的方法
      mulQuesionListRef.value.showCard({
        questionList: questionList,
        element: el
      });
    }
  }
};

const openGenerateDialog = () => {
  dialogStore.generateDialogVisible = true;
  dialogStore.sectionId = curSecId.value;
  dialogStore.belongProjectType = 6;
};

const quesGenerating = ref(false);
const taskId = ref('');
const downloadFile = (content: any) => {
  // 让Blob根据内容自动推断类型（保留空对象）
  const blob = new Blob([content], {});

  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  // 保留download属性但设为空，强制触发下载而非跳转
  link.download = '';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

const downloadGenerateQues = async () => {
  try {
    // 获取文件blob响应
    const res: any = await downloadGenerateQuestion(taskId.value);

    // 检查响应是否为blob
    if (res instanceof Blob) {
      // 使用默认文件名
      const filename = `生成问题_${taskId.value}.xlsx`;

      // 创建blob URL
      const blobUrl = URL.createObjectURL(res);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理blob URL
      URL.revokeObjectURL(blobUrl);

      console.log('文件下载成功');
    } else {
      console.error('响应不是blob类型:', typeof res);
      console.error('响应内容:', res);
    }
  } catch (error) {
    console.error('下载失败:', error);
    console.error('错误详情:', error.response || error);
    // 可以添加错误提示UI
  }
};
const errorMessage = ref(false);

let timer = null;
watch(
  () => isQuesGenerating.value,
  () => {
    if (isQuesGenerating.value) {
      errorMessage.value = false;
      timer = setInterval(async () => {
        const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value);
        if (res.success) {
          if (res.data.status == 1) {
            //生成问题结束之后需要请求新的问题列表
            //通知table组件进行请求
            emitter.emit(Event.GENERATE_SUCCESS);
            //成功之后quesGenerating.value为true，用于下载问题
            quesGenerating.value = true;
            isQuesGenerating.value = false;
            taskId.value = res.data.taskId;
            clearInterval(timer);
          } else if (res.data.status == 2) {
            clearInterval(timer);
            isQuesGenerating.value = false;
            quesGenerating.value = false;
            errorMessage.value = true;
          }
        }
        //每五秒请求一次
      }, 5000);
    }
  }
);

onMounted(async () => {
  // 注册事件监听
  emitter.on(Event.ADD_QUESTION, addQuestionFn);
  emitter.on(Event.REMOVE_QUESTION, removeQuestionFn);

  emitter.on(Event.GENERATE, () => {
    isQuesGenerating.value = true;
  });

  // @ts-ignore
  window.handleWord = handleWord;
  openQId.value = route.query.questionId;

  curHeaderMode.value = Number(sessionStorage.getItem('mode'));

  // 如果是项目内容模式，显示加载动画
  if (curHeaderMode.value === 0) {
    const loadingInstance = ElLoading.service({
      text: '正在努力加载中....',
      background: 'rgba(0, 0, 0, 0)'
    });

    try {
      // 等待DOM更新
      await nextTick();

      // 如果已经有内容，使用 useRenderManager 初始化渲染器
      if (wordContent.value) {
        await initializeRender();
      }

      // 给渲染一些时间
      await new Promise((resolve) => setTimeout(resolve, 300));
    } finally {
      // 关闭加载动画
      loadingInstance.close();
    }
  }

  //每次进入要向后端发送一个请求，查看是否有问题在生成
  //返回一个是否生成的值，默认false，变成true的时候watch监听
  const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value);
  if (res.success) {
    if (res.data.status == 0) {
      isQuesGenerating.value = true;
    } else if (res.data.status == 1) {
      isQuesGenerating.value = false;
      quesGenerating.value = true;
      taskId.value = res.data.taskId;
    } else if (res.data.status == 2) {
      isQuesGenerating.value = false;
      quesGenerating.value = false;
      errorMessage.value = true;
    }
  }
});

onUnmounted(() => {
  // 移除事件监听
  emitter.off(Event.ADD_QUESTION, addQuestionFn);
  emitter.off(Event.REMOVE_QUESTION, removeQuestionFn);

  emitter.off(Event.GENERATE);
  clearInterval(timer);

  sessionStorage.removeItem('mode');

  // 清理render实例
  destroyRenderInstance();
});

// 模式切换和问题列表变化由 useRenderManager 自动处理，无需手动监听

watch(
  () => props,
  // @ts-ignore
  async (newValue, oldValue) => {
    curSecId.value = newValue.curSectionId;
    curPrjId.value = newValue.curProjectId;
    if (getDataFlag.value) {
      // 先等待数据加载完成，再初始化渲染器
      await getSectionData();
      await initializeRender();
    }
    const res: any = await getGenerateCurrent(curPrjId.value, curSecId.value);
    if (res.success) {
      errorMessage.value = false;
      if (res.data.status == 0) {
        isQuesGenerating.value = true;
      } else if (res.data.status == 1) {
        isQuesGenerating.value = false;
        quesGenerating.value = true;
        taskId.value = res.data.taskId;
      } else if (res.data.status == 2) {
        clearInterval(timer);
        isQuesGenerating.value = false;
        quesGenerating.value = false;
        errorMessage.value = true;
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <!-- 主要内容 -->
  <div class="text-question-wrapper">
    <!-- 内容Header -->
    <div class="header-wrapper" style="margin-bottom: 10px">
      <mode-type-switcher :mode="curHeaderMode" @changeMode="handleChangeHeader" :type="'text'">
        <template v-slot:mode0> 文稿内容 </template>
        <template v-slot:mode1> 问题列表 </template>
      </mode-type-switcher>
    </div>
    <div class="btn-container">
      <div v-if="!isQuesGenerating">
        <span class="download" v-if="quesGenerating" @click="downloadGenerateQues">下载问题</span>
        <span style="color: red" v-if="errorMessage">生成问题失败，请重试</span>
        <CmpButton type="primary" class="w130" @click="openGenerateDialog">生成问题</CmpButton>
      </div>
      <div v-else>
        <span style="vertical-align: bottom; margin-right: 5px">正在处理中...</span>
        <div class="w131">生成问题</div>
      </div>
    </div>
    <!-- 内容content -->
    <div class="main-wrapper">
      <!-- 文稿 -->
      <div class="content-wrapper" v-show="curHeaderMode === 0">
        <!--<div class="switch-bar">
          <el-switch v-model="mode" />
          <span class="bar-text">{{ curWordModeText }}</span>
        </div>-->
        <div class="content-container">
          <div>
            <ContentRenderer :content="renderContent" />
          </div>
          <!-- 问号图标 -->
          <div
            v-if="questionIconVisible"
            ref="questionIconElement"
            class="question-icon"
            :style="{
              position: 'fixed',
              left: questionIconPosition.x + 'px',
              top: questionIconPosition.y + 'px',
              zIndex: 10000
            }"
            @click="handleQuestionIconClick"
          >
            <!-- 悬浮提示 -->
            <div class="question-tooltip">提问</div>
            <!-- 问号图标 -->
            <div class="question-icon-circle">
              <img :src="questionIcon" alt="" />
            </div>
          </div>
        </div>
      </div>
      <!-- 问题列表 -->
      <div class="ques-list-wrapper" v-show="curHeaderMode === 1">
        <question-list-table
          :cur-prj-id="curProjectId"
          :cur-sec-id="curSectionId"
        ></question-list-table>
      </div>
    </div>
  </div>
  <!-- 其他组件 -->
  <!-- 问题抽屉 -->
  <!-- <question-drawer
    @refresh="handleRefresh"
    @add="handleAddSubmit"
    @resetElement="handleResetElement"
    ref="questionDrawerRef"
  ></question-drawer> -->
  <!-- 划词浮窗 -->
  <MulQuestionList ref="mulQuesionListRef" id="mul-list"></MulQuestionList>
  <AnswerDialog></AnswerDialog>
  <QuestionDialog></QuestionDialog>
  <GenerateDialog></GenerateDialog>
</template>

<style scoped>
:deep(.el-drawer__header) {
  margin-bottom: 0;
}

.text-question-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 55px;
  width: 100%;
  position: relative;

  .btn-container {
    position: absolute;
    right: 20px;
    top: 10px;

    .download {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: bottom;
      margin-right: 5px;
    }

    .download:hover {
      font-weight: 600;
    }

    .w130 {
      width: 120px;
      height: 35px;
      font-size: 14px;
      border-radius: 4px;
    }

    .w131 {
      width: 120px;
      height: 35px;
      border-radius: 2px;
      font-weight: 400;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      cursor: not-allowed;
      background-color: #dcdfe6;
      border: 1px solid #dcdfe6;
      color: white;
    }
  }

  .main-wrapper {
    width: 100%;
  }

  .content-wrapper {
    flex-direction: column;
    width: 100%;
    word-break: break-all;
    margin-right: 20px;
    display: flex;

    .switch-bar {
      display: flex;
      align-items: center;

      .bar-text {
        margin-left: 10px;
        font-size: 14px;
        font-weight: bold;
        font-family: var(--text-family);
      }
    }

    .content-container {
      padding: 20px 50px;
      width: 100%;
      /*border: 1px solid var(--color-boxborder);*/
      overflow: auto;
      overflow-x: hidden;
    }

    .ques-list-wrapper {
      width: 1100px;
      display: flex;
    }
  }
}
</style>
