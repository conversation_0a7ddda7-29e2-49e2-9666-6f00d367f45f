<script setup lang="ts">

  import {toRef} from "vue";

  const props = defineProps({
    total: {
      type: Number,
      required: true,
    },
    current: {
      type: Number,
      required: true,
    },
    pageSize: {
      type: Number,
      required: true,
    },
  });
  const emit = defineEmits(['changePage']);
  const currentPage = toRef(props, 'current');
  const totalPage = toRef(props, 'total');
  const handleCurrentChange = (value: number) => {
    emit("changePage", value);
  }
</script>

<template>
  <div class="container">
    <span class="dataTotal">共{{ total }}条</span>
    <el-pagination
        @current-change="handleCurrentChange"
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="totalPage"
        :background="true"
        layout="prev, pager, next"
    >
    </el-pagination>
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  height: 46px;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  background-color: var(--color-boxborder);
  border-radius: 2px;
  font-family: var(--font-family-text);
  font-size: 14px;
  .el-pagination {
    &:deep(li), &:deep(button) {
      color: var(--color-black);
      background-color: transparent;
      font-family: var(--font-family-text);
    }
    &:deep(li:hover), &:deep(li.is-active), &:deep(button:hover) {
      color: white;
      background-color: #CCCCCC;
    }
  }
}
</style>