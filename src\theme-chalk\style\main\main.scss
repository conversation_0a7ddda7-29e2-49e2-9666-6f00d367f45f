@use '../../mixins/mixins.scss' as *;

// @include b(main) {
//     margin-top: calc($height__header);
//     margin-left: $width__aside;
//     width: calc(100% - $width__aside);

//     display: flex;
//     flex-direction: column;
//     align-items: center; // 水平居中

//     //debug

//     @include e(wrapper) {
//         width: $width__main;
//         // height: 600px;

//         // background-color: $background-color--primary;

//         @include when(resource) {
//             width: $width__main-short;
//         }
//     }
// }
