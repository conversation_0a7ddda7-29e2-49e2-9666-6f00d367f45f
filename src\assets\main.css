@import 'base.css';
@import 'reset.css';
#app {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
}
#app ::selection {
  background: #1973cb;
  color: #ffffff;
}
.titlefont {
  font-family: var(--title-family);
  font-weight: 600;
  font-style: normal;
}
.textfont {
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  position: relative;
  /* 为绝对定位提供参考 */
}
.flex-start {
  display: flex;
  align-items: flex-start;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, #9fe597, #cce581);
  max-width: 500px;
  text-wrap: wrap;
  color: black;
  z-index: 999 !important;
}
.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
.ellipsis-text {
  display: flex;
  max-width: calc(100% - 10px);
  /* 为引号预留更多空间 */
  color: black;
  /* 确保文字为黑色 */
  position: relative;
  /* 为渐变遮罩定位 */
  align-items: center;
  /* 垂直居中 */
  overflow: hidden;
  height: 100%;
}
/* 省略号文本样式 */
.ellipsis-text-inline {
  max-width: calc(100% - 10px);
  /* 为引号预留更多空间 */
  position: relative;
  /* 为渐变遮罩定位 */
  align-items: center;
  /* 垂直居中 */
  overflow: hidden;
  height: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ellipsis-text::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  /* 渐变区域宽度 */
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
  /* 从透明到白色的渐变 */
  pointer-events: none;
  /* 确保不影响鼠标事件 */
}
.ellipsis-text p,
.ellipsis-text-inline p {
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  /* 垂直居中 */
}
/* 问号图标样式 */
.question-icon {
  cursor: pointer;
  position: relative;
  animation: questionIconFadeIn 0.2s ease-in-out;
}
.question-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.question-icon-circle img {
  width: 16px;
  height: 16px;
}
.question-icon:hover .question-icon-circle {
  background: #f2f2f2;
}
/* 悬浮提示样式 */
.question-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  background: #666666;
  color: white;
  padding: 2px 5px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.question-icon:hover .question-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
/* 覆盖highlight.js库的.highlight样式 */
.highlight {
  color: inherit !important;
  background-color: transparent !important;
  cursor: inherit !important;
}
.highlightHover {
  text-decoration: none !important;
  font-size: 1.02em;
  background-color: #d6e9f6 !important;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 999;
}
.highlightHover .equation {
  background-color: #d6e9f6 !important;
  text-decoration: none;
  font-size: 1.02em;
  transform-origin: center;
  transition: background-color 0.2s ease-in-out;
  position: relative;
  z-index: 10;
}
/* 确保内部元素不继承下划线，但排除floating-content */
.highlightHover *:not(.floating-content):not(.floating-content *) {
  text-decoration: none !important;
}
.title,
.abstract,
.question-content .keyWords,
.question-info-row .keyWords,
.description .keyWords .keyword-container,
.keyWords {
  max-width: 100% !important;
  line-height: 14px !important;
}
.title p,
.abstract p,
.question-content .keyWords p,
.question-info-row .keyWords p,
.description .keyWords .keyword-container p,
.keyWords p {
  display: inline !important;
  margin: 0 !important;
  padding: 0 !important;
}
.title .equation,
.abstract .equation,
.question-content .keyWords .equation,
.question-info-row .keyWords .equation,
.description .keyWords .keyword-container .equation,
.keyWords .equation {
  display: inline-block !important;
  cursor: pointer !important;
  transition: opacity 0.2s ease !important;
  font-family: var(--text-family);
  font-weight: 400;
  font-style: normal;
  font-size: 14px;
  font-size: 10px !important;
}
.title .equation base,
.abstract .equation base,
.question-content .keyWords .equation base,
.question-info-row .keyWords .equation base,
.description .keyWords .keyword-container .equation base,
.keyWords .equation base {
  max-height: 25px !important;
}
.title img,
.abstract img,
.question-content .keyWords img,
.question-info-row .keyWords img,
.description .keyWords .keyword-container img,
.keyWords img {
  display: inline !important;
  height: 14px !important;
  cursor: pointer !important;
  transition: opacity 0.2s ease !important;
}
code:not(.vditor-reset code) {
  padding: 0.065em 0.4em;
  word-break: break-word;
  overflow-x: auto;
  background-color: #fff4f4 !important;
  border-radius: 2px;
  color: #c2185b !important;
}
code:not(.vditor-reset code) span {
  color: #c2185b !important;
}
.select_to_ask_time {
  width: 70px;
  font-weight: 600;
  color: #333333;
  font-size: 12px;
  margin-bottom: 5px;
  user-select: none;
}
.floatingContainer {
  z-index: 10001;
}
.floating-content {
  padding: 8px 12px;
  background: var(--color-primary);
  color: white;
  display: flex;
  justify-content: center;
  flex-direction: column;
  border-radius: 5px;
  font-family: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0', sans-serif;
  font-size: 14px;
  font-weight: 400;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  max-width: 100%;
  overflow: hidden;
  min-width: 150px;
  animation: fadeIn 0.2s ease-in-out;
}
.floating-content .floating-content-item {
  padding: 0;
  transition: all 0.2s ease;
  max-width: 400px;
}
.floating-content .floating-content-item:hover {
  font-weight: 700;
  cursor: pointer;
  transform: translateX(2px);
}
/* 项目名称预览模式样式 */
.preview-container {
  width: 1036px;
  min-height: 35px;
  box-sizing: border-box;
  position: relative;
  padding: 0 12px;
  border: 2px dashed #d0d0d0;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}
.preview-container .preview p,
.preview-container .preview div {
  display: inline-block;
}
.preview-container:hover {
  border-color: var(--color-primary);
  background-color: #f8f9ff;
}
.preview-hint {
  position: absolute;
  bottom: 0px;
  right: 12px;
  font-size: 12px;
  color: #999;
  padding: 0px 5px;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.preview-container:hover .preview-hint {
  opacity: 1;
}
